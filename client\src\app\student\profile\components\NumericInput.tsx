import React from 'react';
import { Input } from '@/components/ui/input';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { createNumericInputHandler } from '../utils/formUtils';

interface NumericInputProps {
  label: string;
  placeholder: string;
  value: string | undefined;
  onChange: (value: string) => void;
  maxLength?: number;
  required?: boolean;
  error?: string;
  className?: string;
}

export const NumericInput: React.FC<NumericInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  maxLength,
  required = false,
  error,
  className = "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
}) => {
  const numericHandler = createNumericInputHandler(onChange, maxLength);

  return (
    <FormItem>
      <FormLabel className="text-black font-medium">
        {label} {required && '*'}
      </FormLabel>
      <FormControl>
        <Input
          value={value || ''}
          className={className}
          placeholder={placeholder}
          type="tel"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={maxLength}
          {...numericHandler}
        />
      </FormControl>
      {error && <FormMessage className="text-red-500">{error}</FormMessage>}
    </FormItem>
  );
};
