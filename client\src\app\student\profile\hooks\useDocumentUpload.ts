import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { UseFormSetError, UseFormSetValue, UseFormClearErrors } from 'react-hook-form';

type DocumentType = File | { name: string; size: number; url: string; type: string } | null;

interface UseDocumentUploadProps {
  setError: UseFormSetError<any>;
  setValue: UseFormSetValue<any>;
  clearErrors: UseFormClearErrors<any>;
}

export const useDocumentUpload = ({ setError, setValue, clearErrors }: UseDocumentUploadProps) => {
  const [uploadedDocument, setUploadedDocument] = useState<DocumentType>(null);
  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);

  const validateDocument = useCallback((file: File): boolean => {
    if (file.size > 5 * 1024 * 1024) {
      setError('document', { message: 'Document size exceeds 5MB limit' });
      return false;
    }

    if (!['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
      setError('document', { message: 'Please upload only PDF, JPG, JPEG, or PNG files' });
      return false;
    }

    return true;
  }, [setError]);

  const handleDocumentUpload = useCallback((file: File) => {
    if (!validateDocument(file)) return;

    const documentWithUrl = {
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
    };

    setUploadedDocument(documentWithUrl);
    setIsDocumentRemoved(false);
    setValue('document', file);
    clearErrors('document');
    toast.success('Document uploaded successfully');
  }, [validateDocument, setValue, clearErrors]);

  const removeDocument = useCallback(() => {
    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {
      URL.revokeObjectURL(uploadedDocument.url);
    }

    setUploadedDocument(null);
    setIsDocumentRemoved(true);
    setValue('document', null);

    const fileInput = document.getElementById('document') as HTMLInputElement;
    if (fileInput) fileInput.value = '';

    toast.success('Document removed successfully');
  }, [uploadedDocument, setValue]);

  const previewDocument = useCallback((document: DocumentType) => {
    if (!document) return;
    
    if ('url' in document) {
      window.open(document.url, '_blank');
    } else {
      const url = URL.createObjectURL(document);
      window.open(url, '_blank');
      setTimeout(() => URL.revokeObjectURL(url), 1000);
    }
  }, []);

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return bytes + ' bytes';
    if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / 1048576).toFixed(1) + ' MB';
  }, []);

  const setExistingDocument = useCallback((documentUrl: string, baseUrl: string) => {
    if (uploadedDocument || isDocumentRemoved) return;

    const fullUrl = documentUrl.startsWith('http') ? documentUrl : `${baseUrl}${documentUrl}`;
    const documentObj = {
      name: documentUrl.split('/').pop() || 'Uploaded Document',
      size: 0,
      url: fullUrl,
      type: 'application/octet-stream',
    };

    setUploadedDocument(documentObj);
    setValue('document', documentObj);
    clearErrors('document');
  }, [uploadedDocument, isDocumentRemoved, setValue, clearErrors]);

  return {
    uploadedDocument,
    isDocumentRemoved,
    handleDocumentUpload,
    removeDocument,
    previewDocument,
    formatFileSize,
    setExistingDocument,
    setUploadedDocument,
    setIsDocumentRemoved
  };
};
