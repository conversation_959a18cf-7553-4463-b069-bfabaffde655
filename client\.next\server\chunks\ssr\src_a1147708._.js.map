{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('studentToken');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};\r\n\r\nexport const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null } => {\r\n  const studentToken = getStudentAuthToken();\r\n  if (studentToken) {\r\n    return { isAuth: true, userType: 'STUDENT' };\r\n  }\r\n\r\n  if (typeof window !== 'undefined') {\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        if (user && user.id) {\r\n          return { isAuth: true, userType: 'CLASS' };\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  return { isAuth: false, userType: null };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX;AAEO,MAAM,kBAAkB;IAC7B,MAAM,eAAe;IACrB,IAAI,cAAc;QAChB,OAAO;YAAE,QAAQ;YAAM,UAAU;QAAU;IAC7C;IAEA,uCAAmC;;IAYnC;IAEA,OAAO;QAAE,QAAQ;QAAO,UAAU;IAAK;AACzC", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAyD;IAC1F,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport {\r\n  format,\r\n  startOfMonth,\r\n  endOfMonth,\r\n  startOfWeek,\r\n  endOfWeek,\r\n  addDays,\r\n  addMonths,\r\n  subMonths,\r\n  isSameMonth,\r\n  isSameDay,\r\n  isToday\r\n} from 'date-fns';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface CalendarProps {\r\n  className?: string;\r\n  selected?: Date;\r\n  onSelect?: (date: Date) => void;\r\n  disabled?: (date: Date) => boolean;\r\n  mode?: 'single' | 'range';\r\n  month?: Date;\r\n  onMonthChange?: (date: Date) => void;\r\n  fromYear?: number;\r\n  toYear?: number;\r\n  captionLayout?: 'buttons' | 'dropdown';\r\n  initialFocus?: boolean;\r\n  classNames?: Record<string, string>;\r\n}\r\n\r\nfunction Calendar({\r\n  className,\r\n  selected,\r\n  onSelect,\r\n  disabled,\r\n  month,\r\n  onMonthChange,\r\n  fromYear,\r\n  toYear,\r\n  captionLayout = 'buttons',\r\n  classNames,\r\n  ...props\r\n}: CalendarProps) {\r\n  const [currentMonth, setCurrentMonth] = React.useState(month || selected || new Date());\r\n\r\n  React.useEffect(() => {\r\n    if (month) {\r\n      setCurrentMonth(month);\r\n    }\r\n  }, [month]);\r\n\r\n  const monthStart = startOfMonth(currentMonth);\r\n  const monthEnd = endOfMonth(monthStart);\r\n  const startDate = startOfWeek(monthStart);\r\n  const endDate = endOfWeek(monthEnd);\r\n\r\n  const dateFormat = 'MMMM yyyy';\r\n  const rows = [];\r\n  let days = [];\r\n  let day = startDate;\r\n  let formattedDate = '';\r\n\r\n  // Generate calendar days\r\n  while (day <= endDate) {\r\n    for (let i = 0; i < 7; i++) {\r\n      formattedDate = format(day, 'd');\r\n      const cloneDay = day;\r\n\r\n      days.push(\r\n        <div\r\n          key={day.toString()}\r\n          className={cn(\r\n            'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer',\r\n            'h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground',\r\n            {\r\n              'text-muted-foreground': !isSameMonth(day, monthStart),\r\n              'bg-primary text-primary-foreground': selected && isSameDay(day, selected),\r\n              'bg-accent text-accent-foreground': isToday(day) && (!selected || !isSameDay(day, selected)),\r\n              'opacity-50 cursor-not-allowed': disabled && disabled(day),\r\n            }\r\n          )}\r\n          onClick={() => {\r\n            if (!disabled || !disabled(cloneDay)) {\r\n              onSelect?.(cloneDay);\r\n            }\r\n          }}\r\n        >\r\n          <span className=\"font-normal\">{formattedDate}</span>\r\n        </div>\r\n      );\r\n      day = addDays(day, 1);\r\n    }\r\n    rows.push(\r\n      <div className=\"flex w-full mt-2\" key={day.toString()}>\r\n        {days}\r\n      </div>\r\n    );\r\n    days = [];\r\n  }\r\n\r\n  const nextMonth = () => {\r\n    const newMonth = addMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const prevMonth = () => {\r\n    const newMonth = subMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(currentMonth.getFullYear(), parseInt(e.target.value), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(parseInt(e.target.value), currentMonth.getMonth(), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-3', className)} {...props}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Header */}\r\n        <div className={cn('flex justify-center pt-1 relative items-center w-full', classNames?.caption)}>\r\n          {captionLayout === 'dropdown' ? (\r\n            <div className=\"flex gap-2\">\r\n              <select\r\n                value={currentMonth.getMonth()}\r\n                onChange={handleMonthChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: 12 }, (_, i) => (\r\n                  <option key={i} value={i}>\r\n                    {format(new Date(2000, i, 1), 'MMMM')}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <select\r\n                value={currentMonth.getFullYear()}\r\n                onChange={handleYearChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: (toYear || new Date().getFullYear()) - (fromYear || 1950) + 1 }, (_, i) => {\r\n                  const year = (fromYear || 1950) + i;\r\n                  return (\r\n                    <option key={year} value={year}>\r\n                      {year}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={prevMonth}\r\n              >\r\n                <ChevronLeft className=\"size-4\" />\r\n              </Button>\r\n              <div className={cn('text-sm font-medium', classNames?.caption_label)}>\r\n                {format(currentMonth, dateFormat)}\r\n              </div>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={nextMonth}\r\n              >\r\n                <ChevronRight className=\"size-4\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        {/* Calendar Grid */}\r\n        <div className=\"w-full border-collapse space-x-1\">\r\n          {/* Days of week header */}\r\n          <div className=\"flex\">\r\n            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (\r\n              <div\r\n                key={day}\r\n                className=\"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center\"\r\n              >\r\n                {day}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Calendar rows */}\r\n          {rows}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAnBA;;;;;;;AAoCA,SAAS,SAAS,EAChB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,aAAa,EACb,QAAQ,EACR,MAAM,EACN,gBAAgB,SAAS,EACzB,UAAU,EACV,GAAG,OACW;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,SAAS,YAAY,IAAI;IAEhF,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,OAAO;YACT,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;IAE1B,MAAM,aAAa;IACnB,MAAM,OAAO,EAAE;IACf,IAAI,OAAO,EAAE;IACb,IAAI,MAAM;IACV,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,MAAO,OAAO,QAAS;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,gBAAgB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;YAC5B,MAAM,WAAW;YAEjB,KAAK,IAAI,eACP,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,oGACA;oBACE,yBAAyB,CAAC,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,KAAK;oBAC3C,sCAAsC,YAAY,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK;oBACjE,oCAAoC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;oBAC3F,iCAAiC,YAAY,SAAS;gBACxD;gBAEF,SAAS;oBACP,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;wBACpC,WAAW;oBACb;gBACF;0BAEA,cAAA,8OAAC;oBAAK,WAAU;8BAAe;;;;;;eAjB1B,IAAI,QAAQ;;;;;YAoBrB,MAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,KAAK;QACrB;QACA,KAAK,IAAI,eACP,8OAAC;YAAI,WAAU;sBACZ;WADoC,IAAI,QAAQ;;;;;QAIrD,OAAO,EAAE;IACX;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,IAAI,KAAK,aAAa,WAAW,IAAI,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG;QAChF,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,IAAI,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,aAAa,QAAQ,IAAI;QAC7E,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QAAa,GAAG,KAAK;kBAC7C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD,YAAY;8BACrF,kBAAkB,2BACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAO,aAAa,QAAQ;gCAC5B,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC;wCAAe,OAAO;kDACpB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,GAAG,IAAI;uCADnB;;;;;;;;;;0CAKjB,8OAAC;gCACC,OAAO,aAAa,WAAW;gCAC/B,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ,CAAC,UAAU,IAAI,OAAO,WAAW,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI;gCAAE,GAAG,CAAC,GAAG;oCACzF,MAAM,OAAO,CAAC,YAAY,IAAI,IAAI;oCAClC,qBACE,8OAAC;wCAAkB,OAAO;kDACvB;uCADU;;;;;gCAIjB;;;;;;;;;;;6CAIJ;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,YAAY;0CACnD,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;0CAExB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;6BAAK,CAAC,GAAG,CAAC,CAAC,oBAC/C,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;wBASV;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAyBF;uCAEe", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'STUDENT_STORE_PURCHASE' | 'STUDENT_STORE_ORDER_APPROVED' | 'STUDENT_STORE_ORDER_REJECTED' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'CLASS_STORE_PURCHASE' | 'CLASS_STORE_ORDER_APPROVED' | 'CLASS_STORE_ORDER_REJECTED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' | 'ADMIN_NEW_STORE_ORDER';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAsCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1181, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n}\r\n\r\nexport default function NotificationBell({ userType }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full\"\r\n          >\r\n            <div className=\"absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\" />\r\n\r\n            <Bell className=\"relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200\" />\r\n\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-semibold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n          <div className=\"p-4 border-b\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <h3 className=\"font-semibold\">Notifications</h3>\r\n              <div className=\"flex gap-2\">\r\n                {unreadCount > 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleMarkAllAsRead}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    Mark all read\r\n                  </Button>\r\n                )}\r\n                {notifications.length > 0 && unreadCount === 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleRemoveAllClick}\r\n                    className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                  >\r\n                    Remove all\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"h-80 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                Loading notifications...\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                No notifications yet\r\n              </div>\r\n            ) : (\r\n              <div className=\"divide-y\">\r\n                {Array.isArray(notifications) && notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.id}\r\n                    className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${!notification.isRead ? 'bg-blue-50/50' : ''\r\n                      }`}\r\n                    onClick={() => handleNotificationClick(notification)}\r\n                  >\r\n                    <div className=\"flex items-start gap-3\">\r\n                      <div className={`w-2 h-2 rounded-full mt-2 ${!notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                        }`} />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                        <p className=\"text-sm text-muted-foreground mt-1\">\r\n                          {notification.message}\r\n                        </p>\r\n                        <p className=\"text-xs text-muted-foreground mt-2\">\r\n                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          {safeNotifications.length > 0 && (\r\n            <div className=\"p-3 border-t bg-muted/30\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"w-full text-xs\"\r\n                onClick={() => {\r\n                  setIsOpen(false);\r\n                  router.push('/notifications');\r\n                }}\r\n              >\r\n                View All Notifications\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </PopoverContent>\r\n      </Popover>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to remove all notifications? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleConfirmRemoveAll}\r\n              className=\"bg-red-600 hover:bg-red-700\"\r\n            >\r\n              Remove All\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AApCA;;;;;;;;;;;AA0Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,IAAI;YACJ,IAAI;YAEJ,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACxC,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;YAClC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;gBAC1C,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;YACpC;YAEA,2CAA2C;YAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;YACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACpD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,MAAM,WAAW,YAAY,oBAAoB;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAmB;IAEvB,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEf,cAAc,mBACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EAAE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IAC1G;4CACJ,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EAAE,CAAC,aAAa,MAAM,GAAG,gBAAgB,eAChF;;;;;;kEACJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAd1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAuB7B,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post('/student/logout');\r\n\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return response.data;\r\n  } catch {\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n      success: true,\r\n      message: 'Logged out successfully',\r\n    };\r\n  }\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;QAE1C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO,SAAS,IAAI;IACtB,EAAE,OAAM;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1796, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\nexport const saveweeklyMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-weekly-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getweeklyMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-weekly-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AACO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,EAAE,CAAC,GAAG;YACpF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,EAAE;YAChF,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,CAAC,WAAW;gBACd,UAAU;gBACV;YACF;YACA,MAAM,WAAmC,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;YACpC,OAAO;gBACL,UAAU;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAU;IAEd,qBACK,8OAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 1929, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/cartApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface CartItem {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS';\r\n  itemId: string;\r\n  quantity: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  item: {\r\n    id: string;\r\n    name: string;\r\n    coinPrice: number;\r\n    image: string | null;\r\n    availableStock: number;\r\n  };\r\n}\r\n\r\nexport interface CartTotal {\r\n  totalCoins: number;\r\n  totalItems: number;\r\n  itemCount: number;\r\n}\r\n\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n  message?: string;\r\n}\r\n\r\nexport const addToCart = async (itemId: string, quantity: number = 1): Promise<ApiResponse<CartItem>> => {\r\n  try {\r\n    const response = await axiosInstance.post('/cart/add', {\r\n      itemId,\r\n      quantity\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to add item to cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Get all cart items\r\nexport const getCartItems = async (): Promise<ApiResponse<CartItem[]>> => {\r\n  try {\r\n    const response = await axiosInstance.get('/cart');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to fetch cart items'\r\n    };\r\n  }\r\n};\r\n\r\n// Update cart item quantity\r\nexport const updateCartItemQuantity = async (itemId: string, quantity: number): Promise<ApiResponse<CartItem>> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/cart/item/${itemId}`, {\r\n      quantity\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to update cart item'\r\n    };\r\n  }\r\n};\r\n\r\n// Remove item from cart\r\nexport const removeFromCart = async (itemId: string): Promise<ApiResponse<null>> => {\r\n  try {\r\n    const response = await axiosInstance.delete(`/cart/item/${itemId}`);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to remove item from cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Clear entire cart\r\nexport const clearCart = async (): Promise<ApiResponse<null>> => {\r\n  try {\r\n    const response = await axiosInstance.delete('/cart/clear');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to clear cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Get cart total\r\nexport const getCartTotal = async (): Promise<ApiResponse<CartTotal>> => {\r\n  try {\r\n    const response = await axiosInstance.get('/cart/total');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to get cart total'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAgCO,MAAM,YAAY,OAAO,QAAgB,WAAmB,CAAC;IAClE,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,aAAa;YACrD;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,yBAAyB,OAAO,QAAgB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,QAAQ;QAClE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC5C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2016, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/storePurchaseApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { PurchaseData, StoreOrder } from '@/lib/types';\r\n\r\nexport type { PurchaseData, StoreOrder };\r\n\r\nexport const purchaseItems = async (data: PurchaseData) => {\r\n  try {\r\n    const response = await axiosInstance.post('/store/purchase', data);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Purchase failed'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMyOrders = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/store/orders');\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch orders'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getOrderDetails = async (orderId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/store/orders/${orderId}`);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    console.error('Failed to fetch order details:', error);\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch order details'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAKO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,mBAAmB;QAC7D,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS;QACnE,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF", "debugId": null}}, {"offset": {"line": 2072, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EAAE,GAAG,OAA4D;IACrF,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  Menu,\r\n  X,\r\n  User,\r\n  ShoppingBag,\r\n  Share2,\r\n  UserCircle,\r\n  LayoutDashboard,\r\n  MessageSquare,\r\n  Coins,\r\n  BadgeCent,\r\n  ShoppingCart,\r\n  Plus,\r\n  Minus,\r\n  CreditCard,\r\n} from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\nimport * as cartApi from '@/services/cartApi';\r\nimport * as storePurchaseApi from '@/services/storePurchaseApi';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\r\n\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector(\r\n    (state: RootState) => state.user\r\n  );\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const [classStatus, setClassStatus] = useState<string | null>(null);\r\n  const [cart, setCart] = useState<cartApi.CartItem[]>([]);\r\n  const [showCart, setShowCart] = useState(false);\r\n  const [isCheckingOut, setIsCheckingOut] = useState(false);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n  const [dropdownOpen, setDropdownOpen] = useState(false)\r\n\r\n  const loadCartItems = async () => {\r\n    try {\r\n      const result = await cartApi.getCartItems();\r\n      if (result.success && result.data) {\r\n        setCart(result.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading cart:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem(\"student_data\");\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n      loadCartItems();\r\n    }\r\n\r\n    if (isAuthenticated) {\r\n      loadCartItems();\r\n    }\r\n    const fetchClassStatus = async () => {\r\n      if (isAuthenticated && user?.id) {\r\n        try {\r\n          const response = await axiosInstance.get(`/classes/details/${user.id}`);\r\n          if (response.data && response.data.status) {\r\n            setClassStatus(response.data.status.status);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching class status:', error);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchClassStatus();\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem(\"student_data\");\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n        loadCartItems();\r\n      } else {\r\n        setStudentData(null);\r\n        setCart([]);\r\n      }\r\n    };\r\n\r\n    const handleCartUpdate = () => {\r\n      if (isLoggedIn || isAuthenticated) {\r\n        loadCartItems();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n    window.addEventListener(\"cartUpdated\", handleCartUpdate);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n      window.removeEventListener(\"cartUpdated\", handleCartUpdate);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((_, delta) => {\r\n    if (contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event(\"storage\"));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem(\"student_data\");\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const removeFromCart = async (productId: string) => {\r\n    try {\r\n      const result = await cartApi.removeFromCart(productId);\r\n      if (result.success) {\r\n        await loadCartItems();\r\n        toast.success(\"Item removed from cart!\");\r\n      } else {\r\n        toast.error(result.error || \"Failed to remove item from cart\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from cart:', error);\r\n      toast.error(\"Failed to remove item from cart\");\r\n    }\r\n  };\r\n\r\n  const updateCartQuantity = async (productId: string, newQuantity: number) => {\r\n    try {\r\n      const cartItem = cart.find(item => item.itemId === productId);\r\n\r\n      if (cartItem && newQuantity > cartItem.item.availableStock) {\r\n        toast.error(`Only ${cartItem.item.availableStock} items available in stock`);\r\n        return;\r\n      }\r\n\r\n      const result = await cartApi.updateCartItemQuantity(productId, newQuantity);\r\n      if (result.success) {\r\n        await loadCartItems();\r\n        if (newQuantity === 0) {\r\n          toast.success(\"Item removed from cart!\");\r\n        }\r\n      } else {\r\n        const errorMessage = result.error || \"Failed to update cart item\";\r\n        if (errorMessage.includes(\"stock\") || errorMessage.includes(\"available\")) {\r\n          toast.error(\"Item is out of stock or insufficient quantity available\");\r\n        } else {\r\n          toast.error(errorMessage);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating cart:', error);\r\n      toast.error(\"Failed to update cart item\");\r\n    }\r\n  };\r\n\r\n  const getTotalCartPrice = () => {\r\n    return cart.reduce((total, item) => {\r\n      return total + (item.item.coinPrice * item.quantity);\r\n    }, 0);\r\n  };\r\n\r\n  const handleCheckout = async () => {\r\n    if (cart.length === 0) {\r\n      toast.error(\"Your cart is empty\");\r\n      return;\r\n    }\r\n\r\n    if (!isStudentLoggedIn && !isAuthenticated) {\r\n      toast.error(\"Please login to checkout\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsCheckingOut(true);\r\n\r\n      const cartItems = cart.map(item => ({\r\n        id: item.itemId,\r\n        name: item.item.name,\r\n        coinPrice: item.item.coinPrice,\r\n        quantity: item.quantity,\r\n        image: item.item.image || ''\r\n      }));\r\n\r\n      const totalCoins = getTotalCartPrice();\r\n\r\n      const purchaseData: storePurchaseApi.PurchaseData = {\r\n        cartItems,\r\n        totalCoins\r\n      };\r\n\r\n      const result = await storePurchaseApi.purchaseItems(purchaseData);\r\n\r\n      if (!result.success) {\r\n        if (result.error === 'PROFILE_NOT_APPROVED') {\r\n          const errorMessage = result.data?.message || 'Your profile is not approved yet. Please complete your profile and wait for admin approval.';\r\n          toast.error(errorMessage);\r\n          return;\r\n        }\r\n        throw new Error(result.error);\r\n      }\r\n\r\n      // Clear cart after successful purchase\r\n      await cartApi.clearCart();\r\n      await loadCartItems();\r\n\r\n      toast.success('Purchase completed successfully!');\r\n      setShowCart(false);\r\n\r\n      // Redirect to orders page\r\n      if (isStudentLoggedIn) {\r\n        router.push('/student/my-orders');\r\n      } else {\r\n        router.push('/classes/my-orders');\r\n      }\r\n\r\n    } catch (error: any) {\r\n      console.error('Checkout error:', error);\r\n      toast.error(error.message || 'Checkout failed. Please try again.');\r\n    } finally {\r\n      setIsCheckingOut(false);\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  type NavLink = {\r\n    href?: string;\r\n    label: React.ReactNode;\r\n    dropdown?: { href: string; label: string; isNew?: boolean }[];\r\n    isNew?: boolean;\r\n  };\r\n\r\n  const navLinks: NavLink[] = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\" },\r\n    {\r\n      href: \"/mock-exam-card\", label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n    },\r\n    { href: \"/weekly-exam-card\", label: \"Weekly Quiz\" },\r\n    { href: \"/uwhiz\", label: \"Uwhiz\" },\r\n\r\n    { href: \"/careers\", label: \"Career\" },\r\n    { href: \"/store\", label: \"Store\" },\r\n  ];\r\n\r\n  const classMenuItems = [\r\n    {\r\n      href: \"/classes/profile\",\r\n      icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Profile\",\r\n    },\r\n    {\r\n      href: \"/classes/chat\",\r\n      icon: <MessageSquare className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Messages\",\r\n    },\r\n    {\r\n      href: \"/coins\",\r\n      icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Coins\",\r\n    },\r\n    {\r\n      href: \"/classes/my-orders\",\r\n      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Orders\",\r\n    },\r\n    {\r\n      onClick: accessClassDashboard,\r\n      icon: <LayoutDashboard className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Dashboard\",\r\n    },\r\n    {\r\n      href: \"/classes/referral-dashboard\",\r\n      icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Referral Dashboard\",\r\n    },\r\n    ...(classStatus === 'APPROVED' ? [{\r\n      href: \"/classes/payment\",\r\n      icon: <BadgeCent className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Payment Details\",\r\n    }] : [])\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-16 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={120}\r\n                height={40}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n            <nav className=\"hidden md:flex items-center space-x-6\">\r\n              {navLinks.map((link, idx) =>\r\n                link.dropdown ? (\r\n                  <DropdownMenu key={idx} open={dropdownOpen} onOpenChange={setDropdownOpen}>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        onClick={() => setDropdownOpen((prev) => !prev)}\r\n                        className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400 bg-transparent hover:bg-transparent\"\r\n                      >\r\n                        {link.label}\r\n                        <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2} viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 9l-7 7-7-7\" />\r\n                        </svg>\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n\r\n                    <DropdownMenuContent className=\"w-48 p-0 bg-black border border-gray-700 rounded shadow-lg\">\r\n                      {link.dropdown.map((item: any) => (\r\n                        <DropdownMenuItem\r\n                          key={item.href}\r\n                          asChild\r\n                          onClick={() => setDropdownOpen(false)} // close after click\r\n                        >\r\n                          <Link\r\n                            href={item.href}\r\n                            className=\"flex items-center w-full px-4 py-2 text-sm text-white hover:text-orange-400 hover:bg-black transition\"\r\n                          >\r\n                            {item.label}\r\n                            {item.isNew && (\r\n                              <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                                Trending\r\n                              </span>\r\n                            )}\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                      ))}\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                ) : (\r\n                  <Link\r\n                    key={link.href}\r\n                    href={link.href as string}\r\n                    className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                  >\r\n                    {link.label}\r\n                    {link.isNew && (\r\n                      <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                        Trending\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                )\r\n              )}\r\n            </nav>\r\n\r\n\r\n            <div className=\"flex items-center space-x-3\">\r\n              {isAuthenticated || isStudentLoggedIn ? (\r\n                <>\r\n                  <NotificationBell\r\n                    userType={isAuthenticated ? \"class\" : \"student\"}\r\n                  />\r\n                  {cart.length > 0 && (\r\n                    <div className=\"relative\">\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => setShowCart(true)}\r\n                        className=\"text-white hover:bg-gray-800 rounded-full relative\"\r\n                      >\r\n                        <ShoppingCart className=\"h-5 w-5\" />\r\n\r\n                        <span className=\"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                          {cart.reduce((total, item) => total + item.quantity, 0)}\r\n                        </span>\r\n\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                  <Popover>\r\n                    <PopoverTrigger asChild>\r\n                      <Avatar className=\"cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity\">\r\n                        <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                          {isAuthenticated\r\n                            ? user?.firstName && user?.lastName\r\n                              ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                              : \"CT\"\r\n                            : studentData?.firstName && studentData?.lastName\r\n                              ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                              : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                    </PopoverTrigger>\r\n                    <PopoverContent className=\"w-64 bg-white p-4 rounded-lg shadow-lg\">\r\n                      <div className=\"flex items-center gap-3 mb-4\">\r\n                        <Avatar className=\"h-10 w-10\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                                : \"CT\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                                ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                                : \"ST\"}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName} ${user.lastName}`\r\n                                : user?.className || \"Class Account\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                                ? `${studentData.firstName} ${studentData.lastName}`\r\n                                : \"Student Account\"}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-600\">\r\n                            {isAuthenticated\r\n                              ? user?.contactNo || \"<EMAIL>\"\r\n                              : studentData?.contactNo || \"<EMAIL>\"}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        {isAuthenticated ? (\r\n                          <>\r\n                            {classMenuItems.map((item) => (\r\n                              <Button\r\n                                asChild\r\n                                variant=\"ghost\"\r\n                                className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                key={item.href || item.label}\r\n                              >\r\n                                {item.href ? (\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                ) : (\r\n                                  <div\r\n                                    onClick={item.onClick}\r\n                                    className=\"flex items-center w-full\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </div>\r\n                                )}\r\n                              </Button>\r\n                            ))}\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              onClick={async () => {\r\n                                try {\r\n                                  const response = await axiosInstance.post(\r\n                                    \"/auth-client/logout\",\r\n                                    {}\r\n                                  );\r\n                                  if (response.data.success) {\r\n                                    router.push(\"/\");\r\n                                    dispatch(clearUser());\r\n                                    localStorage.removeItem(\"token\");\r\n                                    toast.success(\"Logged out successfully\");\r\n                                  }\r\n                                } catch (error) {\r\n                                  console.error(\"Logout error:\", error);\r\n                                  toast.error(\"Failed to logout\");\r\n                                }\r\n                              }}\r\n                            >\r\n                              <User className=\"w-5 h-5 mr-2\" />\r\n                              <span>Logout</span>\r\n                            </Button>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <div className=\"space-y-2\">\r\n                              {[\r\n                                {\r\n                                  href: \"/student/profile\",\r\n                                  icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Profile\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/chat\",\r\n                                  icon: (\r\n                                    <MessageSquare className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"Messages\",\r\n                                },\r\n                                {\r\n                                  href: \"/coins\",\r\n                                  icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Coins\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/wishlist\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Wishlist\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/referral-dashboard\",\r\n                                  icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Referral Dashboard\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/my-orders\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Orders\",\r\n                                },\r\n                              ].map((item) => (\r\n                                <Button\r\n                                  asChild\r\n                                  variant=\"ghost\"\r\n                                  className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                  key={item.href}\r\n                                >\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                </Button>\r\n                              ))}\r\n                              <Button\r\n                                onClick={handleStudentLogout}\r\n                                className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              >\r\n                                <User className=\"w-5 h-5 mr-2\" />\r\n                                <span>Logout</span>\r\n                              </Button>\r\n                            </div>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </PopoverContent>\r\n                  </Popover>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <div className=\"hidden md:flex items-center gap-2\">\r\n                    <Button\r\n                      className=\"bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/class/login\">Join as Tutor</Link>\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className=\"bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/student/login\">Student Login</Link>\r\n                    </Button>\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? (\r\n                  <X className=\"h-6 w-6\" />\r\n                ) : (\r\n                  <Menu className=\"h-6 w-6\" />\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n            }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Uest Logo\"\r\n                width={100}\r\n                height={32}\r\n                className=\"rounded-sm\"\r\n              />\r\n              <div className=\"flex items-center gap-2\">\r\n                {(isAuthenticated || isStudentLoggedIn) && (\r\n                  <div className=\"relative\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      onClick={() => {\r\n                        setShowCart(true);\r\n                        toggleMenu();\r\n                      }}\r\n                      className=\"text-orange-400 hover:bg-orange-500/10 rounded-full relative\"\r\n                    >\r\n                      <ShoppingCart className=\"h-5 w-5\" />\r\n                      {cart.length > 0 && (\r\n                        <span className=\"absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                          {cart.reduce((total, item) => total + item.quantity, 0)}\r\n                        </span>\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <X className=\"h-6 w-6\" />\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {(isAuthenticated || isStudentLoggedIn) && (\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex items-center gap-3 p-3 bg-gray-900 rounded-lg\">\r\n                  <Avatar className=\"h-10 w-10\">\r\n                    <AvatarFallback className=\"bg-white text-black\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  <div>\r\n                    <p className=\"font-medium text-white\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName} ${user.lastName}`\r\n                          : user?.className || \"Class Account\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName} ${studentData.lastName}`\r\n                          : \"Student Account\"}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-400\">\r\n                      {isAuthenticated\r\n                        ? user?.contactNo || \"<EMAIL>\"\r\n                        : studentData?.contactNo || \"<EMAIL>\"}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <nav className=\"flex flex-col space-y-2\">\r\n              {navLinks.map((link) =>\r\n                link.dropdown ? (\r\n                  <div key={link.label?.toString()} className=\"group\">\r\n                    <button\r\n                      className=\"flex items-center justify-between w-full gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors\"\r\n                    >\r\n                      <span className=\"flex items-center gap-2\">\r\n                        {typeof link.label === \"string\" ? link.label : link.label}\r\n                      </span>\r\n                      <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2} viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 9l-7 7-7-7\" />\r\n                      </svg>\r\n                    </button>\r\n                    <div className=\"hidden group-hover:block group-focus-within:block\">\r\n                      <div className=\"flex flex-col mt-1 ml-4 border-l border-gray-700\">\r\n                        {link.dropdown.map((item) => (\r\n                          <Link\r\n                            key={item.href}\r\n                            href={item.href}\r\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-300 hover:text-orange-400 hover:bg-black rounded transition\"\r\n                            onClick={toggleMenu}\r\n                          >\r\n                            {item.label}\r\n                            {item.isNew && (\r\n                              <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                                Trending\r\n                              </span>\r\n                            )}\r\n                          </Link>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <Link\r\n                    key={link.href}\r\n                    href={link.href as string}\r\n                    className=\"flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <div className=\"flex items-center gap-3\">\r\n                      {typeof link.label === \"string\" ? link.label : link.label}\r\n                    </div>\r\n                    {link.isNew && (\r\n                      <span className=\"text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                        Trending\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n                )\r\n              )}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-2\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\r\n                          \"/auth-client/logout\",\r\n                          {}\r\n                        );\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-2\">\r\n                  <Button\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n\r\n      {/* Shopping Cart Dialog */}\r\n      <Dialog open={showCart} onOpenChange={setShowCart}>\r\n        <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <ShoppingCart className=\"w-5 h-5\" />\r\n              Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Review your items before checkout\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"space-y-4\">\r\n            {cart.length === 0 ? (\r\n              <div className=\"text-center py-8\">\r\n                <ShoppingCart className=\"w-12 h-12 mx-auto text-muted-foreground mb-4\" />\r\n                <p className=\"text-muted-foreground\">Your cart is empty</p>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {cart.map((item) => (\r\n                  <div key={item.id} className=\"flex items-center gap-4 p-4 border rounded-lg bg-card\">\r\n                    <Image\r\n                      src={\r\n                        item.item.image?.startsWith('http')\r\n                          ? item.item.image\r\n                          : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.item.image?.startsWith('/') ? item.item.image.substring(1) : item.item.image || 'uploads/store/placeholder.jpg'}`\r\n                      }\r\n                      alt={item.item.name}\r\n                      width={60}\r\n                      height={60}\r\n                      className=\"rounded object-cover\"\r\n                      onError={(e) => {\r\n                        const target = e.target as HTMLImageElement;\r\n                        target.src = \"/logo.png\";\r\n                      }}\r\n                    />\r\n                    <div className=\"flex-1\">\r\n                      <h4 className=\"font-medium text-card-foreground\">{item.item.name}</h4>\r\n                      <p className=\"text-orange-500 font-semibold flex items-center\">\r\n                        <Coins className=\"w-4 h-4 mr-1\" />\r\n                        {item.item.coinPrice} coins\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => updateCartQuantity(item.itemId, item.quantity - 1)}\r\n                      >\r\n                        <Minus className=\"w-3 h-3\" />\r\n                      </Button>\r\n                      <span className=\"w-8 text-center\">{item.quantity}</span>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => updateCartQuantity(item.itemId, item.quantity + 1)}\r\n                      >\r\n                        <Plus className=\"w-3 h-3\" />\r\n                      </Button>\r\n                    </div>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"destructive\"\r\n                      onClick={() => removeFromCart(item.itemId)}\r\n                    >\r\n                      Remove\r\n                    </Button>\r\n                  </div>\r\n                ))}\r\n\r\n                <div className=\"border-t pt-4\">\r\n                  <div className=\"flex justify-between items-center text-lg font-semibold\">\r\n                    <span>Total:</span>\r\n                    <span className=\"text-orange-500 flex items-center\">\r\n                      <Coins className=\"w-5 h-5 mr-1\" />\r\n                      {getTotalCartPrice()} coins\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowCart(false)}>\r\n              Continue Shopping\r\n            </Button>\r\n            {cart.length > 0 && (\r\n              <Button\r\n                onClick={handleCheckout}\r\n                disabled={isCheckingOut}\r\n                className=\"bg-orange-500 hover:bg-orange-600 disabled:opacity-50\"\r\n              >\r\n                {isCheckingOut ? (\r\n                  <>\r\n                    <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\" />\r\n                    Processing...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <CreditCard className=\"w-4 h-4 mr-2\" />\r\n                    Checkout ({getTotalCartPrice()} coins)\r\n                  </>\r\n                )}\r\n              </Button>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;AAtDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,QAAqB,MAAM,IAAI;IAElC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAC7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAoB,AAAD;YACxC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,QAAQ,OAAO,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC3B;QACF;QAEA,IAAI,iBAAiB;YACnB;QACF;QACA,MAAM,mBAAmB;YACvB,IAAI,mBAAmB,MAAM,IAAI;gBAC/B,IAAI;oBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;oBACtE,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;wBACzC,eAAe,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM;oBAC5C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;QACF;QAEA;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YACrB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;gBAC3B;YACF,OAAO;gBACL,eAAe;gBACf,QAAQ,EAAE;YACZ;QACF;QAEA,MAAM,mBAAmB;YACvB,IAAI,cAAc,iBAAiB;gBACjC;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,eAAe;QAEvC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,eAAe;QAC5C;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,GAAG;QACpB,IAAI,iBAAiB,GAAG;QACxB,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAsB,AAAD,EAAE;YAC5C,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;YAEnD,IAAI,YAAY,cAAc,SAAS,IAAI,CAAC,cAAc,EAAE;gBAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC;gBAC3E;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,yBAA8B,AAAD,EAAE,WAAW;YAC/D,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,IAAI,gBAAgB,GAAG;oBACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,OAAO;gBACL,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,cAAc;oBACxE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAO,KAAK,MAAM,CAAC,CAAC,OAAO;YACzB,OAAO,QAAS,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,QAAQ;QACrD,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,MAAM,YAAY,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAClC,IAAI,KAAK,MAAM;oBACf,MAAM,KAAK,IAAI,CAAC,IAAI;oBACpB,WAAW,KAAK,IAAI,CAAC,SAAS;oBAC9B,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI;gBAC5B,CAAC;YAED,MAAM,aAAa;YAEnB,MAAM,eAA8C;gBAClD;gBACA;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,gBAA8B,AAAD,EAAE;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,IAAI,OAAO,KAAK,KAAK,wBAAwB;oBAC3C,MAAM,eAAe,OAAO,IAAI,EAAE,WAAW;oBAC7C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBACA,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;YAEA,uCAAuC;YACvC,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAiB,AAAD;YACtB,MAAM;YAEN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,YAAY;YAEZ,0BAA0B;YAC1B,IAAI,mBAAmB;gBACrB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IASA,MAAM,WAAsB;QAC1B;YAAE,MAAM;YAAqB,OAAO;QAAa;QACjD;YACE,MAAM;YAAmB,qBACvB,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;kCAAK;;;;;;oBACL,mCAAqB,8OAAC,8IAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;QAGnE;QACA;YAAE,MAAM;YAAqB,OAAO;QAAc;QAClD;YAAE,MAAM;YAAU,OAAO;QAAQ;QAEjC;YAAE,MAAM;YAAY,OAAO;QAAS;QACpC;YAAE,MAAM;YAAU,OAAO;QAAQ;KAClC;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;QACT;QACA;YACE,SAAS;YACT,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;QACT;WACI,gBAAgB,aAAa;YAAC;gBAChC,MAAM;gBACN,oBAAM,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAC3B,OAAO;YACT;SAAE,GAAG,EAAE;KACR;IAED,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,MAAM,MACnB,KAAK,QAAQ,iBACX,8OAAC,4IAAA,CAAA,eAAY;wCAAW,MAAM;wCAAc,cAAc;;0DACxD,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB,CAAC,OAAS,CAAC;oDAC1C,WAAU;;wDAET,KAAK,KAAK;sEACX,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,aAAa;4DAAG,SAAQ;sEACtF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAK3D,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,WAAU;0DAC5B,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,4IAAA,CAAA,mBAAgB;wDAEf,OAAO;wDACP,SAAS,IAAM,gBAAgB;kEAE/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;;gEAET,KAAK,KAAK;gEACV,KAAK,KAAK,kBACT,8OAAC;oEAAK,WAAU;8EAA+E;;;;;;;;;;;;uDAV9F,KAAK,IAAI;;;;;;;;;;;uCAjBH;;;;6DAqCnB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;uCAN5F,KAAK,IAAI;;;;;;;;;;0CAgBtB,8OAAC;gCAAI,WAAU;;oCACZ,mBAAmB,kCAClB;;0DACE,8OAAC,6IAAA,CAAA,UAAgB;gDACf,UAAU,kBAAkB,UAAU;;;;;;4CAEvC,KAAK,MAAM,GAAG,mBACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;;sEAEV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEAExB,8OAAC;4DAAK,WAAU;sEACb,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;0DAM7D,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;kEAIZ,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;;0EACxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,WAAU;kFAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4EAAC,WAAU;sFACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;kFAGV,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;0FAER,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;0EAKpC,8OAAC;gEAAI,WAAU;0EACZ,gCACC;;wEACG,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,SAAM;gFACL,OAAO;gFACP,SAAQ;gFACR,WAAU;0FAGT,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;oFACH,MAAM,KAAK,IAAI;oFACf,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;yGAGnB,8OAAC;oFACC,SAAS,KAAK,OAAO;oFACrB,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;;+EAhBhB,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;sFAqBhC,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,WAAU;4EACV,SAAS;gFACP,IAAI;oFACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;oFAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;wFACzB,OAAO,IAAI,CAAC;wFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;wFACjB,aAAa,UAAU,CAAC;wFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oFAChB;gFACF,EAAE,OAAO,OAAO;oFACd,QAAQ,KAAK,CAAC,iBAAiB;oFAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gFACd;4EACF;;8FAEA,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;iGAIV;8EACE,cAAA,8OAAC;wEAAI,WAAU;;4EACZ;gFACC;oFACE,MAAM;oFACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;oFAC5B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAE3B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFACvB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACxB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;6EACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,SAAM;oFACL,OAAO;oFACP,SAAQ;oFACR,WAAU;8FAGV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wFACH,MAAM,KAAK,IAAI;wFACf,WAAU;;4FAET,KAAK,IAAI;0GACV,8OAAC;0GAAM,KAAK,KAAK;;;;;;;;;;;;mFAPd,KAAK,IAAI;;;;;0FAWlB,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS;gFACT,WAAU;;kGAEV,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;kGAChB,8OAAC;kGAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qEAUtB;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAe;;;;;;;;;;;8DAG5B,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAiB;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAER,2BACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;;kCACC,8OAAC;wBACC,WAAW,CAAC,uGAAuG,EAAE,aAAa,kBAAkB,oBAChJ;kCAEJ,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;gDACZ,CAAC,mBAAmB,iBAAiB,mBACpC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,YAAY;4DACZ;wDACF;wDACA,WAAU;;0EAEV,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DACvB,KAAK,MAAM,GAAG,mBACb,8OAAC;gEAAK,WAAU;0EACb,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;8DAM/D,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gCAKlB,CAAC,mBAAmB,iBAAiB,mBACpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;0DAGV,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACrC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;kEAER,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,OACb,KAAK,QAAQ,iBACX,8OAAC;4CAAiC,WAAU;;8DAC1C,8OAAC;oDACC,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEACb,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK;;;;;;sEAE3D,8OAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,aAAa;4DAAG,SAAQ;sEACtF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;;;;;;8DAGzD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,KAAK,IAAI;gEACf,WAAU;gEACV,SAAS;;oEAER,KAAK,KAAK;oEACV,KAAK,KAAK,kBACT,8OAAC;wEAAK,WAAU;kFAAiE;;;;;;;+DAP9E,KAAK,IAAI;;;;;;;;;;;;;;;;2CAfd,KAAK,KAAK,EAAE;;;;iEAgCtB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK;;;;;;gDAE1D,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA4D;;;;;;;2CATzE,KAAK,IAAI;;;;;;;;;;8CAkBtB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;sDACE,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,IAAI;wDACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;wDAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;4DACzB,OAAO,IAAI,CAAC;4DACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;4DACjB,aAAa,UAAU,CAAC;4DACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wDAChB;oDACF,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,iBAAiB;wDAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oDACd;oDACA;gDACF;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;;wCAKX,mCACC;sDAEE,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP;oDACA;gDACF;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;;wCAKX,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;0BAInD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAU,cAAc;0BACpC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAY;wCACpB,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;wCAAG;;;;;;;8CAEzE,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;qDAGvC;;oCACG,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KACE,KAAK,IAAI,CAAC,KAAK,EAAE,WAAW,UACxB,KAAK,IAAI,CAAC,KAAK,GACf,GAAG,8DAAwC,2BAA2B,KAAK,IAAI,CAAC,KAAK,EAAE,WAAW,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,iCAAiC;oDAElM,KAAK,KAAK,IAAI,CAAC,IAAI;oDACnB,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,SAAS,CAAC;wDACR,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,GAAG,GAAG;oDACf;;;;;;8DAEF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC,KAAK,IAAI,CAAC,IAAI;;;;;;sEAChE,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,IAAI,CAAC,SAAS;gEAAC;;;;;;;;;;;;;8DAGzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,KAAK,MAAM,EAAE,KAAK,QAAQ,GAAG;sEAE/D,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAK,WAAU;sEAAmB,KAAK,QAAQ;;;;;;sEAChD,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,KAAK,MAAM,EAAE,KAAK,QAAQ,GAAG;sEAE/D,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGpB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,eAAe,KAAK,MAAM;8DAC1C;;;;;;;2CA5CO,KAAK,EAAE;;;;;kDAkDnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB;wDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,YAAY;8CAAQ;;;;;;gCAG5D,KAAK,MAAM,GAAG,mBACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,8BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAwF;;qEAIzG;;0DACE,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;4CAC5B;4CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;uCAEe", "debugId": null}}, {"offset": {"line": 4233, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/separator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = 'horizontal',\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4263, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 4302, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 4713, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/components/sidebar-nav.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\nimport { CheckCircle } from 'lucide-react';\r\n\r\ninterface SidebarNavProps {\r\n  items: { title: string; href: string }[];\r\n  activeSection: string;\r\n  setActiveSection: (section: string) => void;\r\n}\r\n\r\nexport function SidebarNav({ items, activeSection, setActiveSection }: SidebarNavProps) {\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  const isFormCompleted = (title: string) => {\r\n    if (!profileData?.profile) return false;\r\n\r\n    const profile = profileData.profile;\r\n\r\n    switch (title) {\r\n      case \"Personal Info\":\r\n        return !!(profile.student?.firstName && profile.student?.middleName && profile.student?.lastName &&\r\n                 profile.student?.contact && profile.student?.email && profile.birthday &&\r\n                 profile.school && profile.address && profile.medium && profile.classroom &&\r\n                 profile.photo && profile.documentUrl);\r\n      case \"Other Info\":\r\n        return !!(profile.aadhaarNo || profile.bloodGroup || profile.birthPlace ||\r\n                 profile.motherTongue || profile.religion || profile.caste || profile.subCaste);\r\n      default:\r\n        return false;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <nav className=\"space-y-1\">\r\n      {items.map((item, index) => {\r\n        const isActive = activeSection === item.href.replace('#', '');\r\n        const isCompleted = isFormCompleted(item.title);\r\n        \r\n        // Disable if previous form is not completed (except for first item)\r\n        const isDisabled = index > 0 && !isFormCompleted(items[index - 1].title);\r\n\r\n        return (\r\n          <button\r\n            key={item.href}\r\n            onClick={() => !isDisabled && setActiveSection(item.href.replace('#', ''))}\r\n            className={`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${\r\n              isActive \r\n                ? 'bg-muted text-primary' \r\n                : isDisabled \r\n                ? 'text-gray-400 cursor-not-allowed' \r\n                : 'text-muted-foreground hover:text-primary'\r\n            }`}\r\n            disabled={isDisabled}\r\n          >\r\n            <span>{item.title}</span>\r\n            {isCompleted && <CheckCircle size={16} className=\"text-green-500\" />}\r\n          </button>\r\n        );\r\n      })}\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAQO,SAAS,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,gBAAgB,EAAmB;IACpF,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,aAAa,SAAS,OAAO;QAElC,MAAM,UAAU,YAAY,OAAO;QAEnC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE,cAAc,QAAQ,OAAO,EAAE,YAC/E,QAAQ,OAAO,EAAE,WAAW,QAAQ,OAAO,EAAE,SAAS,QAAQ,QAAQ,IACtE,QAAQ,MAAM,IAAI,QAAQ,OAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ,SAAS,IACxE,QAAQ,KAAK,IAAI,QAAQ,WAAW;YAC/C,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,IAC9D,QAAQ,YAAY,IAAI,QAAQ,QAAQ,IAAI,QAAQ,KAAK,IAAI,QAAQ,QAAQ;YACxF;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,WAAW,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;YAC1D,MAAM,cAAc,gBAAgB,KAAK,KAAK;YAE9C,oEAAoE;YACpE,MAAM,aAAa,QAAQ,KAAK,CAAC,gBAAgB,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK;YAEvE,qBACE,8OAAC;gBAEC,SAAS,IAAM,CAAC,cAAc,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;gBACtE,WAAW,CAAC,uGAAuG,EACjH,WACI,0BACA,aACA,qCACA,4CACJ;gBACF,UAAU;;kCAEV,8OAAC;kCAAM,KAAK,KAAK;;;;;;oBAChB,6BAAe,8OAAC,2NAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;;eAZ5C,KAAK,IAAI;;;;;QAepB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 4782, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/hooks/useCamera.ts"], "sourcesContent": ["import { useState, useRef, useCallback } from 'react';\nimport { toast } from 'sonner';\n\nexport const useCamera = () => {\n  const [isCameraOpen, setIsCameraOpen] = useState(false);\n  const [cameraError, setCameraError] = useState<string | null>(null);\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  const compressImage = useCallback((canvas: HTMLCanvasElement): string => {\n    const context = canvas.getContext('2d');\n    if (!context) return '';\n\n    const maxWidth = 800;\n    const { width, height } = canvas;\n\n    if (width <= maxWidth) {\n      return canvas.toDataURL('image/jpeg', 0.7);\n    }\n\n    const newWidth = maxWidth;\n    const newHeight = (height * maxWidth) / width;\n\n    const compressedCanvas = document.createElement('canvas');\n    compressedCanvas.width = newWidth;\n    compressedCanvas.height = newHeight;\n\n    const compressedContext = compressedCanvas.getContext('2d')!;\n    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);\n\n    return compressedCanvas.toDataURL('image/jpeg', 0.7);\n  }, []);\n\n  const openCamera = useCallback(async () => {\n    setCameraError(null);\n    setIsCameraOpen(true);\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { facingMode: 'user' }\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        videoRef.current.play();\n      }\n    } catch (error: any) {\n      setIsCameraOpen(false);\n      const message = error.name === 'NotAllowedError'\n        ? 'Please allow camera access in your browser settings.'\n        : 'Could not access camera.';\n      setCameraError(message);\n      toast.error(message);\n    }\n  }, []);\n\n  const capturePhoto = useCallback((onCapture: (photoDataUrl: string) => void) => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext('2d')!;\n\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    context.save();\n    context.scale(-1, 1);\n    context.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);\n    context.restore();\n\n    const photoDataUrl = compressImage(canvas);\n    onCapture(photoDataUrl);\n    toast.success('Photo captured successfully');\n    closeCamera();\n  }, [compressImage]);\n\n  const closeCamera = useCallback(() => {\n    if (videoRef.current?.srcObject) {\n      const stream = videoRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      videoRef.current.srcObject = null;\n    }\n    setIsCameraOpen(false);\n    setCameraError(null);\n  }, []);\n\n  return {\n    isCameraOpen,\n    cameraError,\n    videoRef,\n    canvasRef,\n    openCamera,\n    capturePhoto,\n    closeCamera\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,YAAY;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,UAAU,OAAO,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,WAAW;QACjB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QAE1B,IAAI,SAAS,UAAU;YACrB,OAAO,OAAO,SAAS,CAAC,cAAc;QACxC;QAEA,MAAM,WAAW;QACjB,MAAM,YAAY,AAAC,SAAS,WAAY;QAExC,MAAM,mBAAmB,SAAS,aAAa,CAAC;QAChD,iBAAiB,KAAK,GAAG;QACzB,iBAAiB,MAAM,GAAG;QAE1B,MAAM,oBAAoB,iBAAiB,UAAU,CAAC;QACtD,kBAAkB,SAAS,CAAC,QAAQ,GAAG,GAAG,UAAU;QAEpD,OAAO,iBAAiB,SAAS,CAAC,cAAc;IAClD,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,eAAe;QACf,gBAAgB;QAEhB,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBAAE,YAAY;gBAAO;YAC9B;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,SAAS,OAAO,CAAC,IAAI;YACvB;QACF,EAAE,OAAO,OAAY;YACnB,gBAAgB;YAChB,MAAM,UAAU,MAAM,IAAI,KAAK,oBAC3B,yDACA;YACJ,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,QAAQ,IAAI;QACZ,QAAQ,KAAK,CAAC,CAAC,GAAG;QAClB,QAAQ,SAAS,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACtE,QAAQ,OAAO;QAEf,MAAM,eAAe,cAAc;QACnC,UAAU;QACV,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,SAAS,OAAO,EAAE,WAAW;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QACA,gBAAgB;QAChB,eAAe;IACjB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4874, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/hooks/useDocumentUpload.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { toast } from 'sonner';\nimport { UseFormSetError, UseFormSetValue, UseFormClearErrors } from 'react-hook-form';\n\ntype DocumentType = File | { name: string; size: number; url: string; type: string } | null;\n\ninterface UseDocumentUploadProps {\n  setError: UseFormSetError<any>;\n  setValue: UseFormSetValue<any>;\n  clearErrors: UseFormClearErrors<any>;\n}\n\nexport const useDocumentUpload = ({ setError, setValue, clearErrors }: UseDocumentUploadProps) => {\n  const [uploadedDocument, setUploadedDocument] = useState<DocumentType>(null);\n  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);\n\n  const validateDocument = useCallback((file: File): boolean => {\n    if (file.size > 5 * 1024 * 1024) {\n      setError('document', { message: 'Document size exceeds 5MB limit' });\n      return false;\n    }\n\n    if (!['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {\n      setError('document', { message: 'Please upload only PDF, JPG, JPEG, or PNG files' });\n      return false;\n    }\n\n    return true;\n  }, [setError]);\n\n  const handleDocumentUpload = useCallback((file: File) => {\n    if (!validateDocument(file)) return;\n\n    const documentWithUrl = {\n      name: file.name,\n      size: file.size,\n      type: file.type,\n      url: URL.createObjectURL(file),\n    };\n\n    setUploadedDocument(documentWithUrl);\n    setIsDocumentRemoved(false);\n    setValue('document', file);\n    clearErrors('document');\n    toast.success('Document uploaded successfully');\n  }, [validateDocument, setValue, clearErrors]);\n\n  const removeDocument = useCallback(() => {\n    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {\n      URL.revokeObjectURL(uploadedDocument.url);\n    }\n\n    setUploadedDocument(null);\n    setIsDocumentRemoved(true);\n    setValue('document', null);\n\n    const fileInput = document.getElementById('document') as HTMLInputElement;\n    if (fileInput) fileInput.value = '';\n\n    toast.success('Document removed successfully');\n  }, [uploadedDocument, setValue]);\n\n  const previewDocument = useCallback((document: DocumentType) => {\n    if (!document) return;\n    \n    if ('url' in document) {\n      window.open(document.url, '_blank');\n    } else {\n      const url = URL.createObjectURL(document);\n      window.open(url, '_blank');\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    }\n  }, []);\n\n  const formatFileSize = useCallback((bytes: number): string => {\n    if (bytes < 1024) return bytes + ' bytes';\n    if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';\n    return (bytes / 1048576).toFixed(1) + ' MB';\n  }, []);\n\n  const setExistingDocument = useCallback((documentUrl: string, baseUrl: string) => {\n    if (uploadedDocument || isDocumentRemoved) return;\n\n    const fullUrl = documentUrl.startsWith('http') ? documentUrl : `${baseUrl}${documentUrl}`;\n    const documentObj = {\n      name: documentUrl.split('/').pop() || 'Uploaded Document',\n      size: 0,\n      url: fullUrl,\n      type: 'application/octet-stream',\n    };\n\n    setUploadedDocument(documentObj);\n    setValue('document', documentObj);\n    clearErrors('document');\n  }, [uploadedDocument, isDocumentRemoved, setValue, clearErrors]);\n\n  return {\n    uploadedDocument,\n    isDocumentRemoved,\n    handleDocumentUpload,\n    removeDocument,\n    previewDocument,\n    formatFileSize,\n    setExistingDocument,\n    setUploadedDocument,\n    setIsDocumentRemoved\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAWO,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAA0B;IAC3F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,SAAS,YAAY;gBAAE,SAAS;YAAkC;YAClE,OAAO;QACT;QAEA,IAAI,CAAC;YAAC;YAAmB;YAAc;YAAa;SAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACpF,SAAS,YAAY;gBAAE,SAAS;YAAkD;YAClF,OAAO;QACT;QAEA,OAAO;IACT,GAAG;QAAC;KAAS;IAEb,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,CAAC,iBAAiB,OAAO;QAE7B,MAAM,kBAAkB;YACtB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,KAAK,IAAI,eAAe,CAAC;QAC3B;QAEA,oBAAoB;QACpB,qBAAqB;QACrB,SAAS,YAAY;QACrB,YAAY;QACZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB,GAAG;QAAC;QAAkB;QAAU;KAAY;IAE5C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAU;YAC7F,IAAI,eAAe,CAAC,iBAAiB,GAAG;QAC1C;QAEA,oBAAoB;QACpB,qBAAqB;QACrB,SAAS,YAAY;QAErB,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,WAAW,UAAU,KAAK,GAAG;QAEjC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB,GAAG;QAAC;QAAkB;KAAS;IAE/B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,WAAU;QAEf,IAAI,SAAS,WAAU;YACrB,OAAO,IAAI,CAAC,UAAS,GAAG,EAAE;QAC5B,OAAO;YACL,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,OAAO,IAAI,CAAC,KAAK;YACjB,WAAW,IAAM,IAAI,eAAe,CAAC,MAAM;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,IAAI,QAAQ,MAAM,OAAO,QAAQ;QACjC,IAAI,QAAQ,SAAS,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;QACxD,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;IACxC,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,aAAqB;QAC5D,IAAI,oBAAoB,mBAAmB;QAE3C,MAAM,UAAU,YAAY,UAAU,CAAC,UAAU,cAAc,GAAG,UAAU,aAAa;QACzF,MAAM,cAAc;YAClB,MAAM,YAAY,KAAK,CAAC,KAAK,GAAG,MAAM;YACtC,MAAM;YACN,KAAK;YACL,MAAM;QACR;QAEA,oBAAoB;QACpB,SAAS,YAAY;QACrB,YAAY;IACd,GAAG;QAAC;QAAkB;QAAmB;QAAU;KAAY;IAE/D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4989, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/hooks/usePhotoUpload.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { toast } from 'sonner';\nimport { UseFormSetError, UseFormSetValue, UseFormClearErrors } from 'react-hook-form';\n\ninterface UsePhotoUploadProps {\n  setError: UseFormSetError<any>;\n  setValue: UseFormSetValue<any>;\n  clearErrors: UseFormClearErrors<any>;\n  onPhotoUpdate?: (photo: string) => void;\n}\n\nexport const usePhotoUpload = ({ setError, setValue, clearErrors, onPhotoUpdate }: UsePhotoUploadProps) => {\n  const [photo, setPhoto] = useState<string | null>(null);\n\n  const validatePhoto = useCallback((file: File): boolean => {\n    const maxSize = 5 * 1024 * 1024;\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];\n\n    if (file.size > maxSize) {\n      setError('photo', { message: 'Photo size exceeds 5MB limit' });\n      return false;\n    }\n\n    if (!allowedTypes.includes(file.type)) {\n      setError('photo', { message: 'Please upload only JPG, JPEG, or PNG files' });\n      return false;\n    }\n\n    return true;\n  }, [setError]);\n\n  const handlePhotoUpload = useCallback(async (file: File) => {\n    if (!validatePhoto(file)) return;\n\n    try {\n      const reader = new FileReader();\n\n      reader.onload = (e) => {\n        const result = e.target?.result as string;\n        setPhoto(result);\n        setValue('photo', result);\n        clearErrors('photo');\n        onPhotoUpdate?.(result);\n        toast.success('Photo uploaded successfully');\n      };\n\n      reader.onerror = () => {\n        toast.error('Failed to read photo file');\n      };\n\n      reader.readAsDataURL(file);\n    } catch (error) {\n      toast.error('Error uploading photo');\n    }\n  }, [validatePhoto, setValue, clearErrors, onPhotoUpdate]);\n\n  const handlePhotoCapture = useCallback((photoDataUrl: string) => {\n    setPhoto(photoDataUrl);\n    setValue('photo', photoDataUrl);\n    clearErrors('photo');\n    onPhotoUpdate?.(photoDataUrl);\n  }, [setValue, clearErrors, onPhotoUpdate]);\n\n  const setExistingPhoto = useCallback((photoUrl: string) => {\n    if (photo) return;\n    \n    setPhoto(photoUrl);\n    setValue('photo', photoUrl);\n    clearErrors('photo');\n  }, [photo, setValue, clearErrors]);\n\n  return {\n    photo,\n    handlePhotoUpload,\n    handlePhotoCapture,\n    setExistingPhoto,\n    setPhoto\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAUO,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAuB;IACpG,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,UAAU,IAAI,OAAO;QAC3B,MAAM,eAAe;YAAC;YAAc;YAAa;SAAY;QAE7D,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS,SAAS;gBAAE,SAAS;YAA+B;YAC5D,OAAO;QACT;QAEA,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,SAAS,SAAS;gBAAE,SAAS;YAA6C;YAC1E,OAAO;QACT;QAEA,OAAO;IACT,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI,CAAC,cAAc,OAAO;QAE1B,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,SAAS;gBACT,SAAS,SAAS;gBAClB,YAAY;gBACZ,gBAAgB;gBAChB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,OAAO,OAAO,GAAG;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,OAAO,aAAa,CAAC;QACvB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;QAAe;QAAU;QAAa;KAAc;IAExD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,SAAS;QACT,SAAS,SAAS;QAClB,YAAY;QACZ,gBAAgB;IAClB,GAAG;QAAC;QAAU;QAAa;KAAc;IAEzC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,OAAO;QAEX,SAAS;QACT,SAAS,SAAS;QAClB,YAAY;IACd,GAAG;QAAC;QAAO;QAAU;KAAY;IAEjC,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5080, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/utils/formUtils.ts"], "sourcesContent": ["export const calculateAge = (birthday: Date): number => {\n  const today = new Date();\n  let age = today.getFullYear() - birthday.getFullYear();\n  const monthDiff = today.getMonth() - birthday.getMonth();\n\n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthday.getDate())) {\n    age--;\n  }\n\n  return age;\n};\n\nexport const createNumericInputHandler = (onChange: (value: string) => void, maxLength?: number) => {\n  return {\n    onKeyDown: (e: React.KeyboardEvent) => {\n      const specialKeys = [\n        'Backspace', 'Tab', 'Enter', 'Escape', 'Delete',\n        'ArrowLeft', 'ArrowRight', 'Home', 'End',\n      ];\n      \n      if (specialKeys.includes(e.key)) return;\n      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) return;\n      if (!/^\\d$/.test(e.key)) e.preventDefault();\n    },\n    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {\n      const value = e.target.value.replace(/\\D/g, '');\n      const finalValue = maxLength ? value.slice(0, maxLength) : value;\n      onChange(finalValue);\n    }\n  };\n};\n\nexport const capitalizeFirstLetter = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const formatFormData = (data: any) => {\n  return {\n    ...data,\n    firstName: capitalizeFirstLetter(data.firstName),\n    middleName: capitalizeFirstLetter(data.middleName),\n    lastName: capitalizeFirstLetter(data.lastName),\n    birthday: data.birthday?.toISOString() || '',\n  };\n};\n\nexport const calculateProgress = (profile: any): number => {\n  if (!profile) return 0;\n  \n  let completedSections = 0;\n  const totalSections = 2;\n\n  // Personal info section\n  if (profile.student?.firstName && profile.student?.lastName && profile.student?.contact &&\n      profile.student?.email && profile.birthday && profile.school && profile.address &&\n      profile.medium && profile.classroom && profile.photo && profile.documentUrl) {\n    completedSections++;\n  }\n\n  // Other info section\n  if (profile.aadhaarNo || profile.bloodGroup || profile.birthPlace ||\n      profile.motherTongue || profile.religion || profile.caste || profile.subCaste) {\n    completedSections++;\n  }\n\n  return (completedSections / totalSections) * 100;\n};\n"], "names": [], "mappings": ";;;;;;;AAAO,MAAM,eAAe,CAAC;IAC3B,MAAM,QAAQ,IAAI;IAClB,IAAI,MAAM,MAAM,WAAW,KAAK,SAAS,WAAW;IACpD,MAAM,YAAY,MAAM,QAAQ,KAAK,SAAS,QAAQ;IAEtD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,SAAS,OAAO,IAAK;QAC9E;IACF;IAEA,OAAO;AACT;AAEO,MAAM,4BAA4B,CAAC,UAAmC;IAC3E,OAAO;QACL,WAAW,CAAC;YACV,MAAM,cAAc;gBAClB;gBAAa;gBAAO;gBAAS;gBAAU;gBACvC;gBAAa;gBAAc;gBAAQ;aACpC;YAED,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;YACjC,IAAI,EAAE,OAAO,IAAI;gBAAC;gBAAK;gBAAK;gBAAK;aAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;YACrE,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,cAAc;QAC3C;QACA,UAAU,CAAC;YACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;YAC5C,MAAM,aAAa,YAAY,MAAM,KAAK,CAAC,GAAG,aAAa;YAC3D,SAAS;QACX;IACF;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO;QACL,GAAG,IAAI;QACP,WAAW,sBAAsB,KAAK,SAAS;QAC/C,YAAY,sBAAsB,KAAK,UAAU;QACjD,UAAU,sBAAsB,KAAK,QAAQ;QAC7C,UAAU,KAAK,QAAQ,EAAE,iBAAiB;IAC5C;AACF;AAEO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI,oBAAoB;IACxB,MAAM,gBAAgB;IAEtB,wBAAwB;IACxB,IAAI,QAAQ,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE,YAAY,QAAQ,OAAO,EAAE,WAC5E,QAAQ,OAAO,EAAE,SAAS,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO,IAC/E,QAAQ,MAAM,IAAI,QAAQ,SAAS,IAAI,QAAQ,KAAK,IAAI,QAAQ,WAAW,EAAE;QAC/E;IACF;IAEA,qBAAqB;IACrB,IAAI,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,IAC7D,QAAQ,YAAY,IAAI,QAAQ,QAAQ,IAAI,QAAQ,KAAK,IAAI,QAAQ,QAAQ,EAAE;QACjF;IACF;IAEA,OAAO,AAAC,oBAAoB,gBAAiB;AAC/C", "debugId": null}}, {"offset": {"line": 5158, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/components/PhotoUpload.tsx"], "sourcesContent": ["import React, { useRef } from 'react';\nimport { Camera, Upload, Check, X } from 'lucide-react';\nimport Image from 'next/image';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\nimport { FormControl, FormDescription, FormItem, FormMessage } from '@/components/ui/form';\nimport { useCamera } from '../hooks/useCamera';\nimport { usePhotoUpload } from '../hooks/usePhotoUpload';\n\ninterface PhotoUploadProps {\n  photo: string | null;\n  profilePhoto?: string;\n  onPhotoUpload: (file: File) => void;\n  onPhotoCapture: (photoDataUrl: string) => void;\n  error?: string;\n}\n\nexport const PhotoUpload: React.FC<PhotoUploadProps> = ({\n  photo,\n  profilePhoto,\n  onPhotoUpload,\n  onPhotoCapture,\n  error\n}) => {\n  const fileInputRef = useRef<HTMLInputElement | null>(null);\n  const camera = useCamera();\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) onPhotoUpload(file);\n  };\n\n  const handleCapture = () => {\n    camera.capturePhoto(onPhotoCapture);\n  };\n\n  const displayPhoto = photo || profilePhoto;\n\n  return (\n    <Card className={`shadow-lg w-full max-w-full overflow-hidden ${error ? 'border-red-500 border-2' : 'border-0'}`}>\n      <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg\">\n        <CardTitle className=\"text-base sm:text-lg font-medium text-gray-800\">Student Image *</CardTitle>\n        <CardDescription className=\"text-gray-600 text-sm\">\n          Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <FormItem>\n          <FormControl>\n            <div>\n              {camera.cameraError && (\n                <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n                  <p className=\"text-red-700 text-sm\">{camera.cameraError}</p>\n                </div>\n              )}\n              \n              {!camera.isCameraOpen && !displayPhoto && (\n                <Button\n                  type=\"button\"\n                  onClick={camera.openCamera}\n                  className=\"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2\"\n                >\n                  <Camera className=\"h-5 w-5 mr-2\" />\n                  Open Camera\n                </Button>\n              )}\n              \n              {camera.isCameraOpen && (\n                <div className=\"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm\">\n                  <video\n                    ref={camera.videoRef}\n                    autoPlay\n                    playsInline\n                    className=\"w-full h-auto transform scale-x-[-1]\"\n                  />\n                  <div className=\"flex flex-col sm:flex-row gap-2 p-4 bg-gray-50\">\n                    <Button\n                      type=\"button\"\n                      onClick={handleCapture}\n                      variant=\"default\"\n                      className=\"flex-1 bg-black hover:bg-gray-800 text-white\"\n                    >\n                      <Check className=\"h-4 w-4 mr-2\" />\n                      Capture\n                    </Button>\n                    <Button\n                      type=\"button\"\n                      onClick={camera.closeCamera}\n                      variant=\"outline\"\n                      className=\"flex-1 border-gray-300\"\n                    >\n                      <X className=\"h-4 w-4 mr-2\" />\n                      Cancel\n                    </Button>\n                  </div>\n                </div>\n              )}\n              \n              {!camera.isCameraOpen && displayPhoto && (\n                <div className=\"flex flex-col sm:flex-row items-center gap-4\">\n                  <div className=\"border rounded-lg shadow-md bg-gray-50 p-3 w-full max-w-xs mx-auto\">\n                    <div className=\"flex justify-center\">\n                      <Image\n                        src={\n                          displayPhoto.startsWith('data:')\n                            ? displayPhoto\n                            : displayPhoto.startsWith('http')\n                            ? displayPhoto\n                            : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`\n                        }\n                        alt=\"Student Photo\"\n                        height={1000}\n                        width={1000}\n                        className=\"w-full max-w-full h-auto max-h-60 sm:max-h-80 object-contain rounded-lg\"\n                        style={{ height: 'auto', width: 'auto' }}\n                        unoptimized={displayPhoto.startsWith('data:')}\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n              \n              <canvas ref={camera.canvasRef} style={{ display: 'none' }} />\n            </div>\n          </FormControl>\n          <FormDescription className=\"text-xs text-gray-500 mt-2\">\n            A clear photo helps us identify you and personalize your profile\n          </FormDescription>\n          <FormMessage className=\"text-red-500\" />\n        </FormItem>\n        \n        <div className=\"flex flex-col sm:flex-row justify-center gap-2 sm:gap-3 px-2\">\n          <Input\n            type=\"file\"\n            accept=\".jpg,.jpeg,.png\"\n            ref={fileInputRef}\n            className=\"hidden\"\n            onChange={handleFileChange}\n          />\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => fileInputRef.current?.click()}\n            className=\"border-gray-300 px-4 py-2 w-full sm:w-auto\"\n          >\n            <Upload className=\"h-4 w-4 mr-2\" />\n            {displayPhoto ? 'Change Photo' : 'Upload Photo'}\n          </Button>\n\n          <Button\n            type=\"button\"\n            onClick={camera.openCamera}\n            variant=\"outline\"\n            className=\"border-gray-300 px-4 py-2 w-full sm:w-auto\"\n          >\n            <Camera className=\"h-4 w-4 mr-2\" />\n            {displayPhoto ? 'Retake Photo' : 'Take Photo'}\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAWO,MAAM,cAA0C,CAAC,EACtD,KAAK,EACL,YAAY,EACZ,aAAa,EACb,cAAc,EACd,KAAK,EACN;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACrD,MAAM,SAAS,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM,cAAc;IAC1B;IAEA,MAAM,gBAAgB;QACpB,OAAO,YAAY,CAAC;IACtB;IAEA,MAAM,eAAe,SAAS;IAE9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,4CAA4C,EAAE,QAAQ,4BAA4B,YAAY;;0BAC9G,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAiD;;;;;;kCACtE,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAwB;;;;;;;;;;;;0BAIrD,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;;wCACE,OAAO,WAAW,kBACjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,WAAW;;;;;;;;;;;wCAI1D,CAAC,OAAO,YAAY,IAAI,CAAC,8BACxB,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,OAAO,UAAU;4CAC1B,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAKtC,OAAO,YAAY,kBAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAK,OAAO,QAAQ;oDACpB,QAAQ;oDACR,WAAW;oDACX,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS;4DACT,SAAQ;4DACR,WAAU;;8EAEV,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,OAAO,WAAW;4DAC3B,SAAQ;4DACR,WAAU;;8EAEV,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAOrC,CAAC,OAAO,YAAY,IAAI,8BACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KACE,aAAa,UAAU,CAAC,WACpB,eACA,aAAa,UAAU,CAAC,UACxB,eACA,GAAG,8DAAwC,2BAA2B,aAAa,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;wDAEpH,KAAI;wDACJ,QAAQ;wDACR,OAAO;wDACP,WAAU;wDACV,OAAO;4DAAE,QAAQ;4DAAQ,OAAO;wDAAO;wDACvC,aAAa,aAAa,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;sDAO/C,8OAAC;4CAAO,KAAK,OAAO,SAAS;4CAAE,OAAO;gDAAE,SAAS;4CAAO;;;;;;;;;;;;;;;;;0CAG5D,8OAAC,gIAAA,CAAA,kBAAe;gCAAC,WAAU;0CAA6B;;;;;;0CAGxD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,QAAO;gCACP,KAAK;gCACL,WAAU;gCACV,UAAU;;;;;;0CAEZ,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,eAAe,iBAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,OAAO,UAAU;gCAC1B,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 5586, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/components/DocumentUpload.tsx"], "sourcesContent": ["import React from 'react';\nimport { FileText, Upload, X } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\nimport { FormControl, FormDescription, FormItem, FormMessage } from '@/components/ui/form';\n\ntype DocumentType = File | { name: string; size: number; url: string; type: string } | null;\n\ninterface DocumentUploadProps {\n  document: DocumentType;\n  onDocumentUpload: (file: File) => void;\n  onDocumentRemove: () => void;\n  onDocumentPreview: (document: DocumentType) => void;\n  formatFileSize: (bytes: number) => string;\n  error?: string;\n}\n\nexport const DocumentUpload: React.FC<DocumentUploadProps> = ({\n  document,\n  onDocumentUpload,\n  onDocumentRemove,\n  onDocumentPreview,\n  formatFileSize,\n  error\n}) => {\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      onDocumentUpload(file);\n    }\n  };\n\n  return (\n    <Card className={`shadow-lg w-full max-w-full overflow-hidden ${error ? 'border-red-500 border-2' : 'border-0'}`}>\n      <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg\">\n        <CardTitle className=\"text-base sm:text-lg font-medium text-gray-800\">Identity Document *</CardTitle>\n        <CardDescription className=\"text-gray-600 text-sm\">\n          Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <FormItem>\n          {!document ? (\n            <FormControl>\n              <div className=\"flex items-center justify-center w-full\">\n                <label className=\"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors\">\n                  <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                    <Upload className=\"w-10 h-10 mb-3 text-black\" />\n                    <p className=\"mb-2 text-sm text-gray-700\">\n                      <span className=\"font-semibold\">Click to upload</span> or drag and drop\n                    </p>\n                    <p className=\"text-xs text-gray-500\">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>\n                  </div>\n                  <Input\n                    id=\"document\"\n                    type=\"file\"\n                    accept=\".pdf,.jpg,.jpeg,.png\"\n                    className=\"hidden\"\n                    onChange={handleFileChange}\n                  />\n                </label>\n              </div>\n            </FormControl>\n          ) : (\n            <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-[#fff8f3] rounded-full\">\n                    <FileText className=\"h-5 w-5 text-black\" />\n                  </div>\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-700\">{document.name}</p>\n                    <p className=\"text-xs text-gray-500\">\n                      {document instanceof File\n                        ? formatFileSize(document.size)\n                        : 'Previously uploaded document'}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => onDocumentPreview(document)}\n                    className=\"h-8 px-3 border-gray-200 hover:bg-gray-50\"\n                  >\n                    View\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={onDocumentRemove}\n                    className=\"h-8 w-8 p-0 border-gray-200 hover:bg-gray-50\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n          <FormDescription className=\"text-xs text-gray-500 mt-2\">\n            This document will serve to verify your identity and date of birth.\n          </FormDescription>\n          <FormMessage className=\"text-red-500\" />\n        </FormItem>\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAaO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,cAAc,EACd,KAAK,EACN;IACC,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,4CAA4C,EAAE,QAAQ,4BAA4B,YAAY;;0BAC9G,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAiD;;;;;;kCACtE,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAwB;;;;;;;;;;;;0BAIrD,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,gIAAA,CAAA,WAAQ;;wBACN,CAAC,yBACA,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAAsB;;;;;;;8DAExD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,QAAO;4CACP,WAAU;4CACV,UAAU;;;;;;;;;;;;;;;;;;;;;iDAMlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAqC,SAAS,IAAI;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEACV,oBAAoB,OACjB,eAAe,SAAS,IAAI,IAC5B;;;;;;;;;;;;;;;;;;kDAIV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMvB,8OAAC,gIAAA,CAAA,kBAAe;4BAAC,WAAU;sCAA6B;;;;;;sCAGxD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKjC", "debugId": null}}, {"offset": {"line": 5856, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect, Suspense, useCallback } from 'react';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { useForm } from 'react-hook-form';\r\nimport { z } from 'zod';\r\nimport { toast } from 'sonner';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { format } from 'date-fns';\r\nimport { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check } from 'lucide-react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { RootState, AppDispatch } from '@/store';\r\nimport { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';\r\nimport { updateProfilePhoto } from '@/store/slices/studentProfileSlice';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\nimport Header from '../../../app-components/Header';\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport { SidebarNav } from \"./components/sidebar-nav\";\r\n\r\n// Custom hooks and utilities\r\nimport { useCamera } from './hooks/useCamera';\r\nimport { useDocumentUpload } from './hooks/useDocumentUpload';\r\nimport { usePhotoUpload } from './hooks/usePhotoUpload';\r\nimport { calculateAge, createNumericInputHandler, formatFormData, calculateProgress } from './utils/formUtils';\r\n\r\n// Custom components\r\nimport { PhotoUpload } from './components/PhotoUpload';\r\nimport { DocumentUpload } from './components/DocumentUpload';\r\nimport { NumericInput } from './components/NumericInput';\r\n\r\nconst profileFormSchema = z.object({\r\n  firstName: z\r\n    .string()\r\n    .min(2, 'First name must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'First name must start with a letter.'),\r\n  middleName: z\r\n    .string()\r\n    .min(2, 'Middle name must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Middle name must start with a letter.'),\r\n  lastName: z\r\n    .string()\r\n    .min(2, 'Last name must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Last name must start with a letter.'),\r\n  mothersName: z\r\n    .string()\r\n    .min(2, 'Mother\\'s name must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Mother\\'s name must start with a letter.')\r\n    .optional(),\r\n  email: z.string().email('Please enter a valid email address.').min(1, 'Email is required'),\r\n  contact: z\r\n    .string()\r\n    .length(10, 'Contact number must be exactly 10 digits.')\r\n    .regex(/^\\d+$/, 'Contact number must contain only digits.'),\r\n  contact2: z\r\n    .string()\r\n    .length(10, 'Contact number must be exactly 10 digits.')\r\n    .regex(/^\\d+$/, 'Contact number must contain only digits.')\r\n    .optional()\r\n    .or(z.literal('')),\r\n  medium: z.string().min(1, 'Medium of instruction is required'),\r\n  classroom: z.string().min(1, 'Standard is required'),\r\n  gender: z.string().optional(),\r\n  birthday: z.date({ required_error: 'Please select your date of birth' }),\r\n  school: z.string().min(2, 'School name must be at least 2 characters.'),\r\n  address: z.string().min(5, 'Address must be at least 5 characters.'),\r\n  age: z.string().optional(),\r\n  aadhaarNumber: z\r\n    .string()\r\n    .min(12, 'Aadhaar number must be 12 digits.')\r\n    .max(12, 'Aadhaar number must be 12 digits.')\r\n    .regex(/^\\d+$/, 'Aadhaar number must contain only digits.')\r\n    .optional()\r\n    .or(z.literal('')),\r\n  bloodGroup: z.string().optional(),\r\n  birthPlace: z\r\n    .string()\r\n    .min(2, 'Birth place must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Birth place must start with a letter.')\r\n    .optional(),\r\n  motherTongue: z\r\n    .string()\r\n    .min(2, 'Mother tongue must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Mother tongue must start with a letter.')\r\n    .optional(),\r\n  religion: z\r\n    .string()\r\n    .min(2, 'Religion must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Religion must start with a letter.')\r\n    .optional(),\r\n  caste: z\r\n    .string()\r\n    .min(2, 'Caste must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Caste must start with a letter.')\r\n    .optional(),\r\n  subCaste: z\r\n    .string()\r\n    .min(2, 'Sub caste must be at least 2 characters.')\r\n    .regex(/^[a-zA-Z]/, 'Sub caste must start with a letter.')\r\n    .optional(),\r\n  photo: z.any().optional(),\r\n  document: z.any().optional(),\r\n});\r\n\r\ntype ProfileFormValues = z.infer<typeof profileFormSchema>;\r\n\r\nconst sidebarNavItems = [\r\n  {\r\n    title: \"Personal Info\",\r\n    href: \"#personal-info\",\r\n  },\r\n  {\r\n    title: \"Other Info\",\r\n    href: \"#other-info\",\r\n  },\r\n];\r\n\r\nconst StudentProfileContent = () => {\r\n  const router = useRouter();\r\n  const dispatch = useDispatch<AppDispatch>();\r\n  const searchParams = useSearchParams();\r\n  const fromQuiz = searchParams.get('quiz') === 'true';\r\n  const examId = searchParams.get('examId');\r\n\r\n  const [activeSection, setActiveSection] = useState('personal-info');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [progress, setProgress] = useState(0);\r\n  const fileInputRef = useRef<HTMLInputElement | null>(null);\r\n\r\n  const { profileData, loading: profileLoading } = useSelector(\r\n    (state: RootState) => state.studentProfile\r\n  );\r\n\r\n  const profile = profileData?.profile || null;\r\n  const classroomOptions = profileData?.classroomOptions || [];\r\n\r\n  const form = useForm<ProfileFormValues>({\r\n    resolver: zodResolver(profileFormSchema),\r\n    defaultValues: {\r\n      firstName: '',\r\n      middleName: '',\r\n      lastName: '',\r\n      mothersName: '',\r\n      email: '',\r\n      contact: '',\r\n      contact2: '',\r\n      medium: '',\r\n      classroom: '',\r\n      gender: '',\r\n      birthday: undefined,\r\n      school: '',\r\n      address: '',\r\n      age: '',\r\n      aadhaarNumber: '',\r\n      bloodGroup: '',\r\n      birthPlace: '',\r\n      motherTongue: '',\r\n      religion: '',\r\n      caste: '',\r\n      subCaste: '',\r\n      photo: null,\r\n      document: null,\r\n    },\r\n    mode: 'onSubmit',\r\n  });\r\n\r\n  // Custom hooks\r\n  const camera = useCamera();\r\n  const photoUpload = usePhotoUpload({\r\n    setError: form.setError,\r\n    setValue: form.setValue,\r\n    clearErrors: form.clearErrors,\r\n    onPhotoUpdate: (photo) => dispatch(updateProfilePhoto(photo))\r\n  });\r\n  const documentUpload = useDocumentUpload({\r\n    setError: form.setError,\r\n    setValue: form.setValue,\r\n    clearErrors: form.clearErrors\r\n  });\r\n\r\n  useEffect(() => {\r\n    const studentToken = localStorage.getItem('studentToken');\r\n    if (!studentToken) {\r\n      toast.error('Please login to access your profile');\r\n      router.push('/');\r\n    }\r\n  }, [router]);\r\n\r\n  useEffect(() => {\r\n    const studentToken = localStorage.getItem('studentToken');\r\n    if (studentToken) {\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n  }, [dispatch]);\r\n\r\n  useEffect(() => {\r\n    if (profileData?.profile) {\r\n      setProgress(calculateProgress(profileData.profile));\r\n    }\r\n  }, [profileData]);\r\n\r\n  useEffect(() => {\r\n    if (!profileData) return;\r\n\r\n    const profileObj = profileData.profile;\r\n    const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');\r\n\r\n    const formValues = {\r\n      firstName: studentData?.firstName || '',\r\n      middleName: studentData?.middleName || '',\r\n      lastName: studentData?.lastName || '',\r\n      mothersName: studentData?.mothersName || '',\r\n      email: studentData?.email || '',\r\n      contact: studentData?.contactNo || studentData?.contact|| '',\r\n      contact2: profileObj?.contactNo2 || '',\r\n      medium: profileObj?.medium || '',\r\n      classroom: profileObj?.classroom || '',\r\n      gender: profileObj?.gender || '',\r\n      birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,\r\n      school: profileObj?.school || '',\r\n      address: profileObj?.address || '',\r\n      age: profileObj?.age?.toString() || '',\r\n      aadhaarNumber: profileObj?.aadhaarNo || '',\r\n      bloodGroup: profileObj?.bloodGroup || '',\r\n      birthPlace: profileObj?.birthPlace || '',\r\n      motherTongue: profileObj?.motherTongue || '',\r\n      religion: profileObj?.religion || '',\r\n      caste: profileObj?.caste || '',\r\n      subCaste: profileObj?.subCaste || '',\r\n    };\r\n\r\n    if (profileObj?.photo) {\r\n      photoUpload.setExistingPhoto(profileObj.photo);\r\n    }\r\n\r\n    if (profileObj?.documentUrl) {\r\n      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';\r\n      documentUpload.setExistingDocument(profileObj.documentUrl, baseUrl);\r\n    }\r\n\r\n    const currentValues = form.getValues();\r\n    const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;\r\n    const isEducationalDataMissing = !currentValues.medium || !currentValues.classroom;\r\n\r\n    if (isFormEmpty || isEducationalDataMissing) {\r\n      form.reset(formValues);\r\n    }\r\n  }, [profileData, form, photoUpload.photo, documentUpload.uploadedDocument, documentUpload.isDocumentRemoved]);\r\n\r\n\r\n\r\n\r\n\r\n  const onSubmit = async (data: ProfileFormValues) => {\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      const currentPhoto = photoUpload.photo || profileData?.profile?.photo;\r\n      if (!currentPhoto) {\r\n        form.setError('photo', { message: 'Student photo is required. Please capture or upload a photo.' });\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const hasDocument = documentUpload.uploadedDocument || (profileData?.profile?.documentUrl && !documentUpload.isDocumentRemoved);\r\n      if (!hasDocument) {\r\n        form.setError('document', { message: 'Identity document is required. Please upload a document.' });\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (!(await form.trigger())) {\r\n        toast.error('Please fill in all required fields correctly');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const jsonData: any = formatFormData(data);\r\n\r\n      if (photoUpload.photo?.startsWith('data:')) {\r\n        jsonData.photo = photoUpload.photo.split(',')[1];\r\n        jsonData.photoMimeType = 'image/jpeg';\r\n      }\r\n\r\n      if (documentUpload.uploadedDocument instanceof File || (documentUpload.uploadedDocument && 'url' in documentUpload.uploadedDocument && documentUpload.uploadedDocument.url.startsWith('blob:'))) {\r\n        const documentFile = documentUpload.uploadedDocument instanceof File\r\n          ? documentUpload.uploadedDocument\r\n          : await fetch(documentUpload.uploadedDocument.url)\r\n              .then(res => res.blob())\r\n              .then(blob => new File([blob], documentUpload.uploadedDocument!.name, { type: documentUpload.uploadedDocument!.type }));\r\n\r\n        const documentBase64 = await new Promise<string>((resolve) => {\r\n          const reader = new FileReader();\r\n          reader.onload = () => resolve((reader.result as string).split(',')[1]);\r\n          reader.readAsDataURL(documentFile);\r\n        });\r\n\r\n        jsonData.document = documentBase64;\r\n        jsonData.documentMimeType = documentFile.type;\r\n        jsonData.documentName = documentFile.name;\r\n      }\r\n\r\n      if (documentUpload.isDocumentRemoved && profileData?.profile?.documentUrl) {\r\n        jsonData.removeDocument = true;\r\n      }\r\n\r\n      const result = await dispatch(updateStudentProfile(jsonData));\r\n\r\n      if (result.meta.requestStatus === 'fulfilled') {\r\n        toast.success(profile ? 'Profile updated successfully!' : 'Profile created successfully!');\r\n\r\n        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');\r\n        const studentData = {\r\n          ...existingStudentData,\r\n          firstName: data.firstName.charAt(0).toUpperCase() + data.firstName.slice(1).toLowerCase(),\r\n          middleName: data.middleName.charAt(0).toUpperCase() + data.middleName.slice(1).toLowerCase(),\r\n          lastName: data.lastName.charAt(0).toUpperCase() + data.lastName.slice(1).toLowerCase(),\r\n          mothersName: data.mothersName,\r\n          email: data.email || existingStudentData.email,\r\n          contact: data.contact,\r\n        };\r\n\r\n        localStorage.setItem('student_data', JSON.stringify(studentData));\r\n        documentUpload.setIsDocumentRemoved(false);\r\n        await dispatch(fetchStudentProfile());\r\n\r\n        if (fromQuiz && examId) {\r\n          router.push(`/uwhiz-exam/${examId}`);\r\n        } else if (fromQuiz) {\r\n          router.push('/mock-test');\r\n        }\r\n      } else {\r\n        const errorMessage = result.payload as string;\r\n        if (errorMessage?.includes('401')) {\r\n          toast.error('Your session has expired. Please login again.');\r\n          localStorage.removeItem('studentToken');\r\n          router.push('/');\r\n        } else if (errorMessage?.includes('Unique constraint failed on the fields: (`email`)')) {\r\n          toast.error('This email is already registered. Please use a different email address.');\r\n        } else {\r\n          toast.error(errorMessage || 'Failed to update profile');\r\n        }\r\n      }\r\n    } catch {\r\n      toast.error('Failed to submit profile information');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"min-h-screen w-full overflow-x-hidden\">\r\n      <div className=\"space-y-4 p-2 sm:p-4 md:p-6 lg:p-8\">\r\n        <div className=\"space-y-0.5\">\r\n          <h2 className=\"text-xl sm:text-2xl font-bold tracking-tight\">\r\n            Student Profile\r\n          </h2>\r\n          <p className=\"text-muted-foreground\">\r\n            Complete your profile information. Your progress will be automatically saved as you complete each section.\r\n          </p>\r\n        </div>\r\n        <Progress value={progress} className=\"h-2\" />\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          {Math.round(progress)}% complete\r\n        </p>\r\n\r\n        <Separator className=\"my-6\" />\r\n        <div className=\"flex flex-col lg:flex-row gap-4 lg:gap-6\">\r\n          <aside className=\"w-full lg:w-64 xl:w-72 flex-shrink-0\">\r\n            <SidebarNav\r\n              items={sidebarNavItems}\r\n              activeSection={activeSection}\r\n              setActiveSection={setActiveSection}\r\n            />\r\n          </aside>\r\n          <div className=\"flex-1 min-w-0 max-w-full\">\r\n            <div className=\"w-full\">\r\n              {profileLoading ? (\r\n                <div className=\"flex flex-col items-center justify-center py-12\">\r\n                  <svg\r\n                    className=\"animate-spin h-10 w-10 text-black mb-4\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n                    <path\r\n                      className=\"opacity-75\"\r\n                      fill=\"currentColor\"\r\n                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-gray-600\">Loading profile information...</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-6\">\r\n                  <div>\r\n                    <h3 className=\"text-base sm:text-lg font-medium\">\r\n                      {activeSection === 'personal-info' && 'Personal Information'}\r\n                      {activeSection === 'other-info' && 'Other Information'}\r\n                    </h3>\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      {activeSection === 'personal-info' && 'Enter your basic personal details, contact information, medium, standard, photo and documents'}\r\n                      {activeSection === 'other-info' && 'Provide additional details like Aadhaar number, blood group, birth place, etc.'}\r\n                    </p>\r\n                  </div>\r\n                  <Separator />\r\n\r\n                  <Form {...form}>\r\n                    <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6 w-full max-w-full overflow-hidden\">\r\n\r\n                    {/* Personal Information Section */}\r\n                    {activeSection === 'personal-info' && (\r\n                      <div className=\"space-y-4 w-full max-w-full\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 w-full\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"firstName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">First Name *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg w-full min-w-0\"\r\n                                    placeholder=\"Enter First Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"middleName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Middle Name *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Middle Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"lastName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Last Name *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Last Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"mothersName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Mothers Name</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Mother's Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"email\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel className=\"text-black font-medium\">Email *</FormLabel>\r\n                              <FormControl>\r\n                                <Input\r\n                                  {...field}\r\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                  placeholder=\"Enter Email\"\r\n                                  type=\"email\"\r\n                                />\r\n                              </FormControl>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 w-full\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"contact\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Contact Number *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"8520369851\"\r\n                                    type=\"tel\"\r\n                                    inputMode=\"numeric\"\r\n                                    pattern=\"[0-9]*\"\r\n                                    {...createNumericInputHandler(field.onChange, 10)}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"contact2\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Contact Number 2</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Alternate Number\"\r\n                                    type=\"tel\"\r\n                                    inputMode=\"numeric\"\r\n                                    pattern=\"[0-9]*\"\r\n                                    {...createNumericInputHandler(field.onChange, 10)}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 w-full\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"gender\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Gender</FormLabel>\r\n                                <Select\r\n                                  onValueChange={field.onChange}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    <SelectItem value=\"male\">Male</SelectItem>\r\n                                    <SelectItem value=\"female\">Female</SelectItem>\r\n                                    <SelectItem value=\"other\">Other</SelectItem>\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"age\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Age</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Age\"\r\n                                    type=\"number\"\r\n                                    min=\"1\"\r\n                                    max=\"100\"\r\n                                    readOnly\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"birthday\"\r\n                            render={({ field }) => (\r\n                              <FormItem className=\"flex flex-col\">\r\n                                <FormLabel className=\"text-black font-medium\">Date of Birth *</FormLabel>\r\n                              <Popover>\r\n                                <PopoverTrigger asChild>\r\n                                  <FormControl>\r\n                                    <Button\r\n                                      variant=\"outline\"\r\n                                      className={cn(\r\n                                        'w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg',\r\n                                        !field.value && 'text-muted-foreground'\r\n                                      )}\r\n                                    >\r\n                                      {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (\r\n                                        format(field.value, 'PPP')\r\n                                      ) : (\r\n                                        <span>Select your birthday</span>\r\n                                      )}\r\n                                      <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\r\n                                    </Button>\r\n                                  </FormControl>\r\n                                </PopoverTrigger>\r\n                                <PopoverContent className=\"w-auto p-0 bg-white border border-gray-300 shadow-lg\" align=\"start\">\r\n                                  <div className=\"p-3 border-b border-gray-200\">\r\n                                    <div className=\"flex gap-2 mb-3\">\r\n                                      <Select\r\n                                        value={field.value ? field.value.getFullYear().toString() : \"\"}\r\n                                        onValueChange={(year) => {\r\n                                          const currentDate = field.value || new Date();\r\n                                          const newDate = new Date(currentDate);\r\n                                          newDate.setFullYear(parseInt(year));\r\n                                          field.onChange(newDate);\r\n                                        }}\r\n                                      >\r\n                                        <SelectTrigger className=\"w-24\">\r\n                                          <SelectValue placeholder=\"Year\" />\r\n                                        </SelectTrigger>\r\n                                        <SelectContent className=\"max-h-48\">\r\n                                          {Array.from({ length: 125 }, (_, i) => {\r\n                                            const year = new Date().getFullYear() - i;\r\n                                            return (\r\n                                              <SelectItem key={year} value={year.toString()}>\r\n                                                {year}\r\n                                              </SelectItem>\r\n                                            );\r\n                                          })}\r\n                                        </SelectContent>\r\n                                      </Select>\r\n                                      <Select\r\n                                        value={field.value ? field.value.getMonth().toString() : \"\"}\r\n                                        onValueChange={(month) => {\r\n                                          const currentDate = field.value || new Date();\r\n                                          const newDate = new Date(currentDate);\r\n                                          newDate.setMonth(parseInt(month));\r\n                                          field.onChange(newDate);\r\n                                        }}\r\n                                      >\r\n                                        <SelectTrigger className=\"w-32\">\r\n                                          <SelectValue placeholder=\"Month\" />\r\n                                        </SelectTrigger>\r\n                                        <SelectContent>\r\n                                          {[\r\n                                            'January', 'February', 'March', 'April', 'May', 'June',\r\n                                            'July', 'August', 'September', 'October', 'November', 'December'\r\n                                          ].map((month, index) => (\r\n                                            <SelectItem key={index} value={index.toString()}>\r\n                                              {month}\r\n                                            </SelectItem>\r\n                                          ))}\r\n                                        </SelectContent>\r\n                                      </Select>\r\n                                    </div>\r\n                                  </div>\r\n                                  <Calendar\r\n                                    mode=\"single\"\r\n                                    selected={field.value}\r\n                                    onSelect={(date) => {\r\n                                      field.onChange(date);\r\n                                      if (date) {\r\n                                        const age = calculateAge(date);\r\n                                        form.setValue('age', age.toString());\r\n                                      }\r\n                                    }}\r\n                                    disabled={(date) => date > new Date() || date < new Date('1900-01-01')}\r\n                                    month={field.value || new Date()}\r\n                                    className=\"rounded-md border-0\"\r\n                                  />\r\n                                </PopoverContent>\r\n                              </Popover>\r\n                              <FormDescription className=\"text-xs text-gray-500\">\r\n                                Your date of birth will be verified with your documents\r\n                              </FormDescription>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        </div>\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"address\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel className=\"text-black font-medium\">Address *</FormLabel>\r\n                              <FormControl>\r\n                                <Textarea\r\n                                  {...field}\r\n                                  rows={3}\r\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none\"\r\n                                  placeholder=\"Enter your full address\"\r\n                                />\r\n                              </FormControl>\r\n                              <FormDescription className=\"text-xs text-gray-500\">\r\n                                Provide your complete residential address\r\n                              </FormDescription>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"school\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel className=\"text-black font-medium\">School Name *</FormLabel>\r\n                              <FormControl>\r\n                                <Input\r\n                                  {...field}\r\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                  placeholder=\"Enter School\"\r\n                                />\r\n                              </FormControl>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 w-full\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"medium\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Medium *</FormLabel>\r\n                                <Select\r\n                                  onValueChange={(value) => {\r\n                                    field.onChange(value);\r\n                                  }}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    <SelectItem value=\"english\">English</SelectItem>\r\n                                    <SelectItem value=\"gujarati\">Gujarati</SelectItem>\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"classroom\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Standard *</FormLabel>\r\n                                <Select\r\n                                  onValueChange={(value) => {\r\n                                    field.onChange(value);\r\n                                  }}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    {profileLoading ? (\r\n                                      <div className=\"flex items-center justify-center p-4\">\r\n                                        <svg\r\n                                          className=\"animate-spin h-5 w-5 text-black\"\r\n                                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                                          fill=\"none\"\r\n                                          viewBox=\"0 0 24 24\"\r\n                                        >\r\n                                          <circle\r\n                                            className=\"opacity-25\"\r\n                                            cx=\"12\"\r\n                                            cy=\"12\"\r\n                                            r=\"10\"\r\n                                            stroke=\"currentColor\"\r\n                                            strokeWidth=\"4\"\r\n                                          />\r\n                                          <path\r\n                                            className=\"opacity-75\"\r\n                                            fill=\"currentColor\"\r\n                                            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                                          />\r\n                                        </svg>\r\n                                      </div>\r\n                                    ) : classroomOptions.length > 0 ? (\r\n                                      classroomOptions.map((option) => (\r\n                                        <SelectItem key={option.id} value={option.value}>\r\n                                          {option.value}\r\n                                        </SelectItem>\r\n                                      ))\r\n                                    ) : (\r\n                                      <div className=\"p-2 text-center text-gray-500\">No classroom options available</div>\r\n                                    )}\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                              <FormField\r\n                          control={form.control}\r\n                          name=\"photo\"\r\n                          render={({ fieldState }) => (\r\n                            <PhotoUpload\r\n                              photo={photoUpload.photo}\r\n                              profilePhoto={profileData?.profile?.photo}\r\n                              onPhotoUpload={photoUpload.handlePhotoUpload}\r\n                              onPhotoCapture={photoUpload.handlePhotoCapture}\r\n                              error={fieldState.error?.message}\r\n                            />\r\n                      )}\r\n                    />\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"document\"\r\n                      render={({ field, fieldState }) => (\r\n                        <DocumentUpload\r\n                          document={documentUpload.uploadedDocument}\r\n                          onDocumentUpload={(file) => {\r\n                            documentUpload.handleDocumentUpload(file);\r\n                            field.onChange(file);\r\n                          }}\r\n                          onDocumentRemove={documentUpload.removeDocument}\r\n                          onDocumentPreview={documentUpload.previewDocument}\r\n                          formatFileSize={documentUpload.formatFileSize}\r\n                          error={fieldState.error?.message}\r\n                        />\r\n                      )}\r\n                    />\r\n                      </div>\r\n                    )}\r\n\r\n                    {activeSection === 'other-info' && (\r\n                      <div className=\"space-y-4 w-full max-w-full\">\r\n                       <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 w-full\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"aadhaarNumber\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Aadhaar Number</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Aadhaar No\"\r\n                                    type=\"tel\"\r\n                                    inputMode=\"numeric\"\r\n                                    pattern=\"[0-9]*\"\r\n                                    maxLength={12}\r\n                                    {...createNumericInputHandler(field.onChange, 12)}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"bloodGroup\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Blood Group</FormLabel>\r\n                                <Select\r\n                                  onValueChange={field.onChange}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    <SelectItem value=\"A+\">A+</SelectItem>\r\n                                    <SelectItem value=\"A-\">A-</SelectItem>\r\n                                    <SelectItem value=\"B+\">B+</SelectItem>\r\n                                    <SelectItem value=\"B-\">B-</SelectItem>\r\n                                    <SelectItem value=\"AB+\">AB+</SelectItem>\r\n                                    <SelectItem value=\"AB-\">AB-</SelectItem>\r\n                                    <SelectItem value=\"O+\">O+</SelectItem>\r\n                                    <SelectItem value=\"O-\">O-</SelectItem>\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"birthPlace\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Birth Place</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Birth Place\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"motherTongue\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Mother Tongue</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Mother Tongue\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"religion\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Religion</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Religion\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"caste\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Caste</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Caste\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"subCaste\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Sub Caste</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Sub Caste\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div className=\"flex justify-center sm:justify-end pt-6 border-t border-gray-100\">\r\n                      <Button\r\n                        type=\"submit\"\r\n                        disabled={isSubmitting}\r\n                        className=\"bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200 w-full sm:w-auto\"\r\n                      >\r\n                        {isSubmitting ? (\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <svg className=\"animate-spin h-4 w-4\" viewBox=\"0 0 24 24\">\r\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                            </svg>\r\n                            Saving...\r\n                          </div>\r\n                        ) : profileData ? (\r\n                          'Update Profile'\r\n                        ) : (\r\n                          'Save Profile'\r\n                        )}\r\n                      </Button>\r\n                    </div>\r\n\r\n                    </form>\r\n                  </Form>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      </div>\r\n      <Footer />\r\n    </>\r\n  );\r\n};\r\n\r\nconst StudentProfilePage = () => {\r\n  return (\r\n    <Suspense\r\n      fallback={\r\n        <div className=\"min-h-screen flex items-center justify-center\">\r\n          <svg\r\n            className=\"animate-spin h-10 w-10 text-black\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n            <path\r\n              className=\"opacity-75\"\r\n              fill=\"currentColor\"\r\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      }\r\n    >\r\n      <StudentProfileContent />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default StudentProfilePage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AASA;AACA;AACA;AACA;AAQA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA,6BAA6B;AAC7B;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AApDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDA,MAAM,oBAAoB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,oIAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG,6CACP,KAAK,CAAC,aAAa;IACtB,YAAY,oIAAA,CAAA,IAAC,CACV,MAAM,GACN,GAAG,CAAC,GAAG,8CACP,KAAK,CAAC,aAAa;IACtB,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,4CACP,KAAK,CAAC,aAAa;IACtB,aAAa,oIAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG,iDACP,KAAK,CAAC,aAAa,4CACnB,QAAQ;IACX,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,uCAAuC,GAAG,CAAC,GAAG;IACtE,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,MAAM,CAAC,IAAI,6CACX,KAAK,CAAC,SAAS;IAClB,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,MAAM,CAAC,IAAI,6CACX,KAAK,CAAC,SAAS,4CACf,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAE,gBAAgB;IAAmC;IACtE,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,eAAe,oIAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,IAAI,qCACR,GAAG,CAAC,IAAI,qCACR,KAAK,CAAC,SAAS,4CACf,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,oIAAA,CAAA,IAAC,CACV,MAAM,GACN,GAAG,CAAC,GAAG,8CACP,KAAK,CAAC,aAAa,yCACnB,QAAQ;IACX,cAAc,oIAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,gDACP,KAAK,CAAC,aAAa,2CACnB,QAAQ;IACX,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,2CACP,KAAK,CAAC,aAAa,sCACnB,QAAQ;IACX,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,wCACP,KAAK,CAAC,aAAa,mCACnB,QAAQ;IACX,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,4CACP,KAAK,CAAC,aAAa,uCACnB,QAAQ;IACX,OAAO,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;IACvB,UAAU,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AAC5B;AAIA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,wBAAwB;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,aAAa,GAAG,CAAC,YAAY;IAC9C,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAErD,MAAM,EAAE,WAAW,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACzD,CAAC,QAAqB,MAAM,cAAc;IAG5C,MAAM,UAAU,aAAa,WAAW;IACxC,MAAM,mBAAmB,aAAa,oBAAoB,EAAE;IAE5D,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAqB;QACtC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,YAAY;YACZ,UAAU;YACV,aAAa;YACb,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,KAAK;YACL,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA,MAAM;IACR;IAEA,eAAe;IACf,MAAM,SAAS,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,2JAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,UAAU,KAAK,QAAQ;QACvB,UAAU,KAAK,QAAQ;QACvB,aAAa,KAAK,WAAW;QAC7B,eAAe,CAAC,QAAU,SAAS,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;IACxD;IACA,MAAM,iBAAiB,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE;QACvC,UAAU,KAAK,QAAQ;QACvB,UAAU,KAAK,QAAQ;QACvB,aAAa,KAAK,WAAW;IAC/B;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,cAAc;YAChB,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS;YACxB,YAAY,CAAA,GAAA,sJAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,OAAO;QACnD;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;QAElB,MAAM,aAAa,YAAY,OAAO;QACtC,MAAM,cAAc,YAAY,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;QAE9F,MAAM,aAAa;YACjB,WAAW,aAAa,aAAa;YACrC,YAAY,aAAa,cAAc;YACvC,UAAU,aAAa,YAAY;YACnC,aAAa,aAAa,eAAe;YACzC,OAAO,aAAa,SAAS;YAC7B,SAAS,aAAa,aAAa,aAAa,WAAU;YAC1D,UAAU,YAAY,cAAc;YACpC,QAAQ,YAAY,UAAU;YAC9B,WAAW,YAAY,aAAa;YACpC,QAAQ,YAAY,UAAU;YAC9B,UAAU,YAAY,WAAW,IAAI,KAAK,WAAW,QAAQ,IAAI;YACjE,QAAQ,YAAY,UAAU;YAC9B,SAAS,YAAY,WAAW;YAChC,KAAK,YAAY,KAAK,cAAc;YACpC,eAAe,YAAY,aAAa;YACxC,YAAY,YAAY,cAAc;YACtC,YAAY,YAAY,cAAc;YACtC,cAAc,YAAY,gBAAgB;YAC1C,UAAU,YAAY,YAAY;YAClC,OAAO,YAAY,SAAS;YAC5B,UAAU,YAAY,YAAY;QACpC;QAEA,IAAI,YAAY,OAAO;YACrB,YAAY,gBAAgB,CAAC,WAAW,KAAK;QAC/C;QAEA,IAAI,YAAY,aAAa;YAC3B,MAAM,UAAU,8DAAwC;YACxD,eAAe,mBAAmB,CAAC,WAAW,WAAW,EAAE;QAC7D;QAEA,MAAM,gBAAgB,KAAK,SAAS;QACpC,MAAM,cAAc,CAAC,cAAc,SAAS,IAAI,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,OAAO;QACjG,MAAM,2BAA2B,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,SAAS;QAElF,IAAI,eAAe,0BAA0B;YAC3C,KAAK,KAAK,CAAC;QACb;IACF,GAAG;QAAC;QAAa;QAAM,YAAY,KAAK;QAAE,eAAe,gBAAgB;QAAE,eAAe,iBAAiB;KAAC;IAM5G,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,MAAM,eAAe,YAAY,KAAK,IAAI,aAAa,SAAS;YAChE,IAAI,CAAC,cAAc;gBACjB,KAAK,QAAQ,CAAC,SAAS;oBAAE,SAAS;gBAA+D;gBACjG,gBAAgB;gBAChB;YACF;YAEA,MAAM,cAAc,eAAe,gBAAgB,IAAK,aAAa,SAAS,eAAe,CAAC,eAAe,iBAAiB;YAC9H,IAAI,CAAC,aAAa;gBAChB,KAAK,QAAQ,CAAC,YAAY;oBAAE,SAAS;gBAA2D;gBAChG,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAE,MAAM,KAAK,OAAO,IAAK;gBAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,MAAM,WAAgB,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAAE;YAErC,IAAI,YAAY,KAAK,EAAE,WAAW,UAAU;gBAC1C,SAAS,KAAK,GAAG,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAChD,SAAS,aAAa,GAAG;YAC3B;YAEA,IAAI,eAAe,gBAAgB,YAAY,QAAS,eAAe,gBAAgB,IAAI,SAAS,eAAe,gBAAgB,IAAI,eAAe,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,UAAW;gBAC/L,MAAM,eAAe,eAAe,gBAAgB,YAAY,OAC5D,eAAe,gBAAgB,GAC/B,MAAM,MAAM,eAAe,gBAAgB,CAAC,GAAG,EAC5C,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA,OAAQ,IAAI,KAAK;wBAAC;qBAAK,EAAE,eAAe,gBAAgB,CAAE,IAAI,EAAE;wBAAE,MAAM,eAAe,gBAAgB,CAAE,IAAI;oBAAC;gBAE1H,MAAM,iBAAiB,MAAM,IAAI,QAAgB,CAAC;oBAChD,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,AAAC,OAAO,MAAM,CAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrE,OAAO,aAAa,CAAC;gBACvB;gBAEA,SAAS,QAAQ,GAAG;gBACpB,SAAS,gBAAgB,GAAG,aAAa,IAAI;gBAC7C,SAAS,YAAY,GAAG,aAAa,IAAI;YAC3C;YAEA,IAAI,eAAe,iBAAiB,IAAI,aAAa,SAAS,aAAa;gBACzE,SAAS,cAAc,GAAG;YAC5B;YAEA,MAAM,SAAS,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE;YAEnD,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;gBAC7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU,kCAAkC;gBAE1D,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;gBAC/E,MAAM,cAAc;oBAClB,GAAG,mBAAmB;oBACtB,WAAW,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,WAAW;oBACvF,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,WAAW;oBAC1F,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,WAAW;oBACpF,aAAa,KAAK,WAAW;oBAC7B,OAAO,KAAK,KAAK,IAAI,oBAAoB,KAAK;oBAC9C,SAAS,KAAK,OAAO;gBACvB;gBAEA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBACpD,eAAe,oBAAoB,CAAC;gBACpC,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;gBAEjC,IAAI,YAAY,QAAQ;oBACtB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ;gBACrC,OAAO,IAAI,UAAU;oBACnB,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,eAAe,OAAO,OAAO;gBACnC,IAAI,cAAc,SAAS,QAAQ;oBACjC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,cAAc,SAAS,sDAAsD;oBACtF,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC9B;YACF;QACF,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAU,WAAU;;;;;;sCACrC,8OAAC;4BAAE,WAAU;;gCACV,KAAK,KAAK,CAAC;gCAAU;;;;;;;sCAGxB,8OAAC,qIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC,iKAAA,CAAA,aAAU;wCACT,OAAO;wCACP,eAAe;wCACf,kBAAkB;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,+BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAM;oDACN,MAAK;oDACL,SAAQ;;sEAER,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DACC,WAAU;4DACV,MAAK;4DACL,GAAE;;;;;;;;;;;;8DAGN,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;iEAG/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;gEACX,kBAAkB,mBAAmB;gEACrC,kBAAkB,gBAAgB;;;;;;;sEAErC,8OAAC;4DAAE,WAAU;;gEACV,kBAAkB,mBAAmB;gEACrC,kBAAkB,gBAAgB;;;;;;;;;;;;;8DAGvC,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAEV,8OAAC,gIAAA,CAAA,OAAI;oDAAE,GAAG,IAAI;8DACZ,cAAA,8OAAC;wDAAK,UAAU,KAAK,YAAY,CAAC;wDAAW,WAAU;;4DAGtD,kBAAkB,iCACjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;;;;;;;;;;;0GAGhB,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;;;;;;;;;;;0GAGhB,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;;;;;;;;;;;0GAGhB,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;;;;;;;;;;;0GAGhB,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kFAK/B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;4FACZ,MAAK;;;;;;;;;;;kGAGT,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;oGACZ,MAAK;oGACL,WAAU;oGACV,SAAQ;oGACP,GAAG,CAAA,GAAA,sJAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,QAAQ,EAAE,GAAG;;;;;;;;;;;0GAGrD,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;oGACZ,MAAK;oGACL,WAAU;oGACV,SAAQ;oGACP,GAAG,CAAA,GAAA,sJAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,QAAQ,EAAE,GAAG;;;;;;;;;;;0GAGrD,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kFAK/B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,kIAAA,CAAA,SAAM;gGACL,eAAe,MAAM,QAAQ;gGAC7B,OAAO,MAAM,KAAK,IAAI;;kHAEtB,8OAAC,gIAAA,CAAA,cAAW;kHACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;4GAAC,WAAU;sHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gHAAC,aAAY;;;;;;;;;;;;;;;;kHAG7B,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;;0HACvB,8OAAC,kIAAA,CAAA,aAAU;gHAAC,OAAM;0HAAO;;;;;;0HACzB,8OAAC,kIAAA,CAAA,aAAU;gHAAC,OAAM;0HAAS;;;;;;0HAC3B,8OAAC,kIAAA,CAAA,aAAU;gHAAC,OAAM;0HAAQ;;;;;;;;;;;;;;;;;;0GAG9B,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oGACH,GAAG,KAAK;oGACT,WAAU;oGACV,aAAY;oGACZ,MAAK;oGACL,KAAI;oGACJ,KAAI;oGACJ,QAAQ;;;;;;;;;;;0GAGZ,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wFAAC,WAAU;;0GAClB,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAChD,8OAAC,mIAAA,CAAA,UAAO;;kHACN,8OAAC,mIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,8OAAC,gIAAA,CAAA,cAAW;sHACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;gHACL,SAAQ;gHACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,CAAC,MAAM,KAAK,IAAI;;oHAGjB,MAAM,KAAK,IAAI,MAAM,KAAK,YAAY,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,MACvE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,8OAAC;kIAAK;;;;;;kIAER,8OAAC,0MAAA,CAAA,WAAY;wHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kHAI9B,8OAAC,mIAAA,CAAA,iBAAc;wGAAC,WAAU;wGAAuD,OAAM;;0HACrF,8OAAC;gHAAI,WAAU;0HACb,cAAA,8OAAC;oHAAI,WAAU;;sIACb,8OAAC,kIAAA,CAAA,SAAM;4HACL,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,KAAK;4HAC5D,eAAe,CAAC;gIACd,MAAM,cAAc,MAAM,KAAK,IAAI,IAAI;gIACvC,MAAM,UAAU,IAAI,KAAK;gIACzB,QAAQ,WAAW,CAAC,SAAS;gIAC7B,MAAM,QAAQ,CAAC;4HACjB;;8IAEA,8OAAC,kIAAA,CAAA,gBAAa;oIAAC,WAAU;8IACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wIAAC,aAAY;;;;;;;;;;;8IAE3B,8OAAC,kIAAA,CAAA,gBAAa;oIAAC,WAAU;8IACtB,MAAM,IAAI,CAAC;wIAAE,QAAQ;oIAAI,GAAG,CAAC,GAAG;wIAC/B,MAAM,OAAO,IAAI,OAAO,WAAW,KAAK;wIACxC,qBACE,8OAAC,kIAAA,CAAA,aAAU;4IAAY,OAAO,KAAK,QAAQ;sJACxC;2IADc;;;;;oIAIrB;;;;;;;;;;;;sIAGJ,8OAAC,kIAAA,CAAA,SAAM;4HACL,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,GAAG,QAAQ,KAAK;4HACzD,eAAe,CAAC;gIACd,MAAM,cAAc,MAAM,KAAK,IAAI,IAAI;gIACvC,MAAM,UAAU,IAAI,KAAK;gIACzB,QAAQ,QAAQ,CAAC,SAAS;gIAC1B,MAAM,QAAQ,CAAC;4HACjB;;8IAEA,8OAAC,kIAAA,CAAA,gBAAa;oIAAC,WAAU;8IACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wIAAC,aAAY;;;;;;;;;;;8IAE3B,8OAAC,kIAAA,CAAA,gBAAa;8IACX;wIACC;wIAAW;wIAAY;wIAAS;wIAAS;wIAAO;wIAChD;wIAAQ;wIAAU;wIAAa;wIAAW;wIAAY;qIACvD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,8OAAC,kIAAA,CAAA,aAAU;4IAAa,OAAO,MAAM,QAAQ;sJAC1C;2IADc;;;;;;;;;;;;;;;;;;;;;;;;;;;0HAQ3B,8OAAC,oIAAA,CAAA,WAAQ;gHACP,MAAK;gHACL,UAAU,MAAM,KAAK;gHACrB,UAAU,CAAC;oHACT,MAAM,QAAQ,CAAC;oHACf,IAAI,MAAM;wHACR,MAAM,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE;wHACzB,KAAK,QAAQ,CAAC,OAAO,IAAI,QAAQ;oHACnC;gHACF;gHACA,UAAU,CAAC,OAAS,OAAO,IAAI,UAAU,OAAO,IAAI,KAAK;gHACzD,OAAO,MAAM,KAAK,IAAI,IAAI;gHAC1B,WAAU;;;;;;;;;;;;;;;;;;0GAIhB,8OAAC,gIAAA,CAAA,kBAAe;gGAAC,WAAU;0GAAwB;;;;;;0GAGnD,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kFAK7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4FACN,GAAG,KAAK;4FACT,MAAM;4FACN,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,kBAAe;wFAAC,WAAU;kGAAwB;;;;;;kGAGnD,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,kIAAA,CAAA,SAAM;gGACL,eAAe,CAAC;oGACd,MAAM,QAAQ,CAAC;gGACjB;gGACA,OAAO,MAAM,KAAK,IAAI;;kHAEtB,8OAAC,gIAAA,CAAA,cAAW;kHACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;4GAAC,WAAU;sHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gHAAC,aAAY;;;;;;;;;;;;;;;;kHAG7B,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;;0HACvB,8OAAC,kIAAA,CAAA,aAAU;gHAAC,OAAM;0HAAU;;;;;;0HAC5B,8OAAC,kIAAA,CAAA,aAAU;gHAAC,OAAM;0HAAW;;;;;;;;;;;;;;;;;;0GAGjC,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;0FAI7B,8OAAC,gIAAA,CAAA,YAAS;gFACR,SAAS,KAAK,OAAO;gFACrB,MAAK;gFACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,YAAS;gGAAC,WAAU;0GAAyB;;;;;;0GAC9C,8OAAC,kIAAA,CAAA,SAAM;gGACL,eAAe,CAAC;oGACd,MAAM,QAAQ,CAAC;gGACjB;gGACA,OAAO,MAAM,KAAK,IAAI;;kHAEtB,8OAAC,gIAAA,CAAA,cAAW;kHACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;4GAAC,WAAU;sHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gHAAC,aAAY;;;;;;;;;;;;;;;;kHAG7B,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACtB,+BACC,8OAAC;4GAAI,WAAU;sHACb,cAAA,8OAAC;gHACC,WAAU;gHACV,OAAM;gHACN,MAAK;gHACL,SAAQ;;kIAER,8OAAC;wHACC,WAAU;wHACV,IAAG;wHACH,IAAG;wHACH,GAAE;wHACF,QAAO;wHACP,aAAY;;;;;;kIAEd,8OAAC;wHACC,WAAU;wHACV,MAAK;wHACL,GAAE;;;;;;;;;;;;;;;;qHAIN,iBAAiB,MAAM,GAAG,IAC5B,iBAAiB,GAAG,CAAC,CAAC,uBACpB,8OAAC,kIAAA,CAAA,aAAU;gHAAiB,OAAO,OAAO,KAAK;0HAC5C,OAAO,KAAK;+GADE,OAAO,EAAE;;;;wIAK5B,8OAAC;4GAAI,WAAU;sHAAgC;;;;;;;;;;;;;;;;;0GAIrD,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kFAKzB,8OAAC,gIAAA,CAAA,YAAS;wEACd,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,UAAU,EAAE,iBACrB,8OAAC,8JAAA,CAAA,cAAW;gFACV,OAAO,YAAY,KAAK;gFACxB,cAAc,aAAa,SAAS;gFACpC,eAAe,YAAY,iBAAiB;gFAC5C,gBAAgB,YAAY,kBAAkB;gFAC9C,OAAO,WAAW,KAAK,EAAE;;;;;;;;;;;kFAInC,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,iBAC5B,8OAAC,iKAAA,CAAA,iBAAc;gFACb,UAAU,eAAe,gBAAgB;gFACzC,kBAAkB,CAAC;oFACjB,eAAe,oBAAoB,CAAC;oFACpC,MAAM,QAAQ,CAAC;gFACjB;gFACA,kBAAkB,eAAe,cAAc;gFAC/C,mBAAmB,eAAe,eAAe;gFACjD,gBAAgB,eAAe,cAAc;gFAC7C,OAAO,WAAW,KAAK,EAAE;;;;;;;;;;;;;;;;;4DAO9B,kBAAkB,8BACjB,8OAAC;gEAAI,WAAU;0EACd,cAAA,8OAAC;oEAAI,WAAU;;sFACZ,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,WAAU;gGACV,SAAQ;gGACR,WAAW;gGACV,GAAG,CAAA,GAAA,sJAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,QAAQ,EAAE,GAAG;;;;;;;;;;;sGAGrD,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,kIAAA,CAAA,SAAM;4FACL,eAAe,MAAM,QAAQ;4FAC7B,OAAO,MAAM,KAAK,IAAI;;8GAEtB,8OAAC,gIAAA,CAAA,cAAW;8GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,8OAAC,kIAAA,CAAA,gBAAa;oGAAC,WAAU;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAM;;;;;;sHACxB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAM;;;;;;sHACxB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;;;;;;;;;;;;;sGAG3B,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAQnC,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,UAAU;oEACV,WAAU;8EAET,6BACC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;gFAAuB,SAAQ;;kGAC5C,8OAAC;wFAAO,WAAU;wFAAa,IAAG;wFAAK,IAAG;wFAAK,GAAE;wFAAK,QAAO;wFAAe,aAAY;;;;;;kGACxF,8OAAC;wFAAK,WAAU;wFAAa,MAAK;wFAAe,GAAE;;;;;;;;;;;;4EAC/C;;;;;;+EAGN,cACF,mBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcpB,8OAAC,mIAAA,CAAA,UAAM;;;;;;;AAGb;AAEA,MAAM,qBAAqB;IACzB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QACP,wBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;;;;;;kBAMV,cAAA,8OAAC;;;;;;;;;;AAGP;uCAEe", "debugId": null}}]}