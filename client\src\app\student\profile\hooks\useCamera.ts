import { useState, useRef, useCallback } from 'react';
import { toast } from 'sonner';

export const useCamera = () => {
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const compressImage = useCallback((canvas: HTMLCanvasElement): string => {
    const context = canvas.getContext('2d');
    if (!context) return '';

    const maxWidth = 800;
    const { width, height } = canvas;

    if (width <= maxWidth) {
      return canvas.toDataURL('image/jpeg', 0.7);
    }

    const newWidth = maxWidth;
    const newHeight = (height * maxWidth) / width;

    const compressedCanvas = document.createElement('canvas');
    compressedCanvas.width = newWidth;
    compressedCanvas.height = newHeight;

    const compressedContext = compressedCanvas.getContext('2d')!;
    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);

    return compressedCanvas.toDataURL('image/jpeg', 0.7);
  }, []);

  const openCamera = useCallback(async () => {
    setCameraError(null);
    setIsCameraOpen(true);

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }
    } catch (error: any) {
      setIsCameraOpen(false);
      const message = error.name === 'NotAllowedError'
        ? 'Please allow camera access in your browser settings.'
        : 'Could not access camera.';
      setCameraError(message);
      toast.error(message);
    }
  }, []);

  const capturePhoto = useCallback((onCapture: (photoDataUrl: string) => void) => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d')!;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    context.save();
    context.scale(-1, 1);
    context.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
    context.restore();

    const photoDataUrl = compressImage(canvas);
    onCapture(photoDataUrl);
    toast.success('Photo captured successfully');
    closeCamera();
  }, [compressImage]);

  const closeCamera = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsCameraOpen(false);
    setCameraError(null);
  }, []);

  return {
    isCameraOpen,
    cameraError,
    videoRef,
    canvasRef,
    openCamera,
    capturePhoto,
    closeCamera
  };
};
