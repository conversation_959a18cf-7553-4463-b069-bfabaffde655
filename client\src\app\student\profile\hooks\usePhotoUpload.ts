import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { UseFormSetError, UseFormSetValue, UseFormClearErrors } from 'react-hook-form';

interface UsePhotoUploadProps {
  setError: UseFormSetError<any>;
  setValue: UseFormSetValue<any>;
  clearErrors: UseFormClearErrors<any>;
  onPhotoUpdate?: (photo: string) => void;
}

export const usePhotoUpload = ({ setError, setValue, clearErrors, onPhotoUpdate }: UsePhotoUploadProps) => {
  const [photo, setPhoto] = useState<string | null>(null);

  const validatePhoto = useCallback((file: File): boolean => {
    const maxSize = 5 * 1024 * 1024;
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

    if (file.size > maxSize) {
      setError('photo', { message: 'Photo size exceeds 5MB limit' });
      return false;
    }

    if (!allowedTypes.includes(file.type)) {
      setError('photo', { message: 'Please upload only JPG, JPEG, or PNG files' });
      return false;
    }

    return true;
  }, [setError]);

  const handlePhotoUpload = useCallback(async (file: File) => {
    if (!validatePhoto(file)) return;

    try {
      const reader = new FileReader();

      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPhoto(result);
        setValue('photo', result);
        clearErrors('photo');
        onPhotoUpdate?.(result);
        toast.success('Photo uploaded successfully');
      };

      reader.onerror = () => {
        toast.error('Failed to read photo file');
      };

      reader.readAsDataURL(file);
    } catch (error) {
      toast.error('Error uploading photo');
    }
  }, [validatePhoto, setValue, clearErrors, onPhotoUpdate]);

  const handlePhotoCapture = useCallback((photoDataUrl: string) => {
    setPhoto(photoDataUrl);
    setValue('photo', photoDataUrl);
    clearErrors('photo');
    onPhotoUpdate?.(photoDataUrl);
  }, [setValue, clearErrors, onPhotoUpdate]);

  const setExistingPhoto = useCallback((photoUrl: string) => {
    if (photo) return;
    
    setPhoto(photoUrl);
    setValue('photo', photoUrl);
    clearErrors('photo');
  }, [photo, setValue, clearErrors]);

  return {
    photo,
    handlePhotoUpload,
    handlePhotoCapture,
    setExistingPhoto,
    setPhoto
  };
};
