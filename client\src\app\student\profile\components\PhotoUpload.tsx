import React, { useRef } from 'react';
import { Camera, Upload, Check, X } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FormControl, FormDescription, FormItem, FormMessage } from '@/components/ui/form';
import { useCamera } from '../hooks/useCamera';
import { usePhotoUpload } from '../hooks/usePhotoUpload';

interface PhotoUploadProps {
  photo: string | null;
  profilePhoto?: string;
  onPhotoUpload: (file: File) => void;
  onPhotoCapture: (photoDataUrl: string) => void;
  error?: string;
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  photo,
  profilePhoto,
  onPhotoUpload,
  onPhotoCapture,
  error
}) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const camera = useCamera();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) onPhotoUpload(file);
  };

  const handleCapture = () => {
    camera.capturePhoto(onPhotoCapture);
  };

  const displayPhoto = photo || profilePhoto;

  return (
    <Card className={`shadow-lg w-full max-w-full overflow-hidden ${error ? 'border-red-500 border-2' : 'border-0'}`}>
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
        <CardTitle className="text-base sm:text-lg font-medium text-gray-800">Student Image *</CardTitle>
        <CardDescription className="text-gray-600 text-sm">
          Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <FormItem>
          <FormControl>
            <div>
              {camera.cameraError && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-700 text-sm">{camera.cameraError}</p>
                </div>
              )}
              
              {!camera.isCameraOpen && !displayPhoto && (
                <Button
                  type="button"
                  onClick={camera.openCamera}
                  className="w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2"
                >
                  <Camera className="h-5 w-5 mr-2" />
                  Open Camera
                </Button>
              )}
              
              {camera.isCameraOpen && (
                <div className="camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <video
                    ref={camera.videoRef}
                    autoPlay
                    playsInline
                    className="w-full h-auto transform scale-x-[-1]"
                  />
                  <div className="flex flex-col sm:flex-row gap-2 p-4 bg-gray-50">
                    <Button
                      type="button"
                      onClick={handleCapture}
                      variant="default"
                      className="flex-1 bg-black hover:bg-gray-800 text-white"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Capture
                    </Button>
                    <Button
                      type="button"
                      onClick={camera.closeCamera}
                      variant="outline"
                      className="flex-1 border-gray-300"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
              
              {!camera.isCameraOpen && displayPhoto && (
                <div className="flex flex-col sm:flex-row items-center gap-4">
                  <div className="border rounded-lg shadow-md bg-gray-50 p-3 w-full max-w-xs mx-auto">
                    <div className="flex justify-center">
                      <Image
                        src={
                          displayPhoto.startsWith('data:')
                            ? displayPhoto
                            : displayPhoto.startsWith('http')
                            ? displayPhoto
                            : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`
                        }
                        alt="Student Photo"
                        height={1000}
                        width={1000}
                        className="w-full max-w-full h-auto max-h-60 sm:max-h-80 object-contain rounded-lg"
                        style={{ height: 'auto', width: 'auto' }}
                        unoptimized={displayPhoto.startsWith('data:')}
                      />
                    </div>
                  </div>
                </div>
              )}
              
              <canvas ref={camera.canvasRef} style={{ display: 'none' }} />
            </div>
          </FormControl>
          <FormDescription className="text-xs text-gray-500 mt-2">
            A clear photo helps us identify you and personalize your profile
          </FormDescription>
          <FormMessage className="text-red-500" />
        </FormItem>
        
        <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-3 px-2">
          <Input
            type="file"
            accept=".jpg,.jpeg,.png"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileChange}
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            className="border-gray-300 px-4 py-2 w-full sm:w-auto"
          >
            <Upload className="h-4 w-4 mr-2" />
            {displayPhoto ? 'Change Photo' : 'Upload Photo'}
          </Button>

          <Button
            type="button"
            onClick={camera.openCamera}
            variant="outline"
            className="border-gray-300 px-4 py-2 w-full sm:w-auto"
          >
            <Camera className="h-4 w-4 mr-2" />
            {displayPhoto ? 'Retake Photo' : 'Take Photo'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
