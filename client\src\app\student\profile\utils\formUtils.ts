export const calculateAge = (birthday: Date): number => {
  const today = new Date();
  let age = today.getFullYear() - birthday.getFullYear();
  const monthDiff = today.getMonth() - birthday.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthday.getDate())) {
    age--;
  }

  return age;
};

export const createNumericInputHandler = (onChange: (value: string) => void, maxLength?: number) => {
  return {
    onKeyDown: (e: React.KeyboardEvent) => {
      const specialKeys = [
        'Backspace', 'Tab', 'Enter', 'Escape', 'Delete',
        'ArrowLeft', 'ArrowRight', 'Home', 'End',
      ];
      
      if (specialKeys.includes(e.key)) return;
      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) return;
      if (!/^\d$/.test(e.key)) e.preventDefault();
    },
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value.replace(/\D/g, '');
      const finalValue = maxLength ? value.slice(0, maxLength) : value;
      onChange(finalValue);
    }
  };
};

export const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const formatFormData = (data: any) => {
  return {
    ...data,
    firstName: capitalizeFirstLetter(data.firstName),
    middleName: capitalizeFirstLetter(data.middleName),
    lastName: capitalizeFirstLetter(data.lastName),
    birthday: data.birthday?.toISOString() || '',
  };
};

export const calculateProgress = (profile: any): number => {
  if (!profile) return 0;
  
  let completedSections = 0;
  const totalSections = 2;

  // Personal info section
  if (profile.student?.firstName && profile.student?.lastName && profile.student?.contact &&
      profile.student?.email && profile.birthday && profile.school && profile.address &&
      profile.medium && profile.classroom && profile.photo && profile.documentUrl) {
    completedSections++;
  }

  // Other info section
  if (profile.aadhaarNo || profile.bloodGroup || profile.birthPlace ||
      profile.motherTongue || profile.religion || profile.caste || profile.subCaste) {
    completedSections++;
  }

  return (completedSections / totalSections) * 100;
};
