[{"G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx": "14", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx": "19", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx": "53", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx": "54", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx": "55", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx": "56", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx": "57", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx": "58", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx": "59", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx": "60", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx": "61", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx": "62", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx": "63", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx": "64", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx": "65", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx": "66", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx": "67", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx": "69", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx": "71", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx": "72", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx": "75", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx": "76", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx": "80", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts": "92", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts": "93", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts": "94", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts": "95", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts": "96", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts": "97", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts": "98", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts": "99", "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx": "100", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts": "101", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts": "102", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts": "103", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts": "104", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts": "105", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts": "106", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts": "107", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts": "108", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts": "110", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts": "111", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts": "112", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts": "113", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts": "114", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts": "115", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx": "116", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts": "117", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts": "118", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts": "119", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts": "120", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx": "121", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx": "122", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx": "126", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts": "127", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx": "128", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx": "129", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx": "130", "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts": "131", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx": "132", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx": "133", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts": "134", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts": "135", "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts": "136", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx": "137", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx": "138", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx": "139", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts": "140", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts": "141", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx": "142", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx": "143", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx": "144", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx": "145", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts": "146", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts": "147", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts": "148", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts": "149", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts": "150", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts": "151", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx": "152", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx": "153", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx": "154", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts": "155", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx": "156", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx": "157", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx": "158", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts": "159", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts": "160", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx": "161", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx": "162", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx": "163", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts": "164", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx": "165", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx": "166", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx": "167", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx": "168", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx": "169", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-otp\\page.tsx": "170", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badgedisplay.tsx": "171", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\streakcountdisplay.tsx": "172", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\totaluestcoin.tsx": "173", "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\getStudentId.tsx": "174", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\LeaderboardUserApi.ts": "175", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mock-exam-resultApi.ts": "176", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mockExamStreakApi.ts": "177", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uestCoinTransctionApi.ts": "178", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamTerminationApi.ts": "179", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\page.tsx": "180", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\my-orders\\page.tsx": "181", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\components\\sidebar-nav.tsx": "182", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\storeApi.ts": "183", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\storePurchaseApi.ts": "184", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-super-kids-result\\page.tsx": "185", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\my-orders\\page.tsx": "186", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\MyOrders.tsx": "187", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\cartApi.ts": "188", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\payment\\page.tsx": "189", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\about\\page.tsx": "190", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\weeklyExam.tsx": "191", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\[id]\\page.tsx": "192", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\weekly-exam-result\\page.tsx": "193", "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\WeeklyExamService.ts": "194", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\weekly-exam-card\\page.tsx": "195", "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\pricing\\page.tsx": "196", "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\SpinningWheel.tsx": "197"}, {"size": 156, "mtime": 1751275811335, "results": "198", "hashOfConfig": "199"}, {"size": 4799, "mtime": 1751275807358, "results": "200", "hashOfConfig": "199"}, {"size": 5240, "mtime": 1751275802728, "results": "201", "hashOfConfig": "199"}, {"size": 29458, "mtime": 1751625232828, "results": "202", "hashOfConfig": "199"}, {"size": 3171, "mtime": 1747109292602, "results": "203", "hashOfConfig": "199"}, {"size": 4002, "mtime": 1754565811926, "results": "204", "hashOfConfig": "199"}, {"size": 226, "mtime": 1751024271963, "results": "205", "hashOfConfig": "199"}, {"size": 5928, "mtime": 1751024275820, "results": "206", "hashOfConfig": "199"}, {"size": 2482, "mtime": 1753702430243, "results": "207", "hashOfConfig": "199"}, {"size": 5384, "mtime": 1747289688954, "results": "208", "hashOfConfig": "199"}, {"size": 3658, "mtime": 1747289688955, "results": "209", "hashOfConfig": "199"}, {"size": 674, "mtime": 1747289688955, "results": "210", "hashOfConfig": "199"}, {"size": 5932, "mtime": 1752465858736, "results": "211", "hashOfConfig": "199"}, {"size": 114, "mtime": 1752596842332, "results": "212", "hashOfConfig": "199"}, {"size": 597, "mtime": 1752596848563, "results": "213", "hashOfConfig": "199"}, {"size": 13660, "mtime": 1749201002331, "results": "214", "hashOfConfig": "199"}, {"size": 550, "mtime": 1747289688983, "results": "215", "hashOfConfig": "199"}, {"size": 1623, "mtime": 1747289688984, "results": "216", "hashOfConfig": "199"}, {"size": 3787, "mtime": 1747990267735, "results": "217", "hashOfConfig": "199"}, {"size": 516, "mtime": 1747289689001, "results": "218", "hashOfConfig": "199"}, {"size": 17426, "mtime": 1752465858750, "results": "219", "hashOfConfig": "199"}, {"size": 551, "mtime": 1747289689003, "results": "220", "hashOfConfig": "199"}, {"size": 14780, "mtime": 1747990267862, "results": "221", "hashOfConfig": "199"}, {"size": 557, "mtime": 1747289689005, "results": "222", "hashOfConfig": "199"}, {"size": 4900, "mtime": 1751625232913, "results": "223", "hashOfConfig": "199"}, {"size": 451, "mtime": 1747289689007, "results": "224", "hashOfConfig": "199"}, {"size": 514, "mtime": 1747289689008, "results": "225", "hashOfConfig": "199"}, {"size": 9870, "mtime": 1748962020705, "results": "226", "hashOfConfig": "199"}, {"size": 9250, "mtime": 1749206244199, "results": "227", "hashOfConfig": "199"}, {"size": 578, "mtime": 1747289689027, "results": "228", "hashOfConfig": "199"}, {"size": 20216, "mtime": 1752486686589, "results": "229", "hashOfConfig": "199"}, {"size": 17836, "mtime": 1754623075219, "results": "230", "hashOfConfig": "199"}, {"size": 21113, "mtime": 1754882480062, "results": "231", "hashOfConfig": "199"}, {"size": 1778, "mtime": 1754565812004, "results": "232", "hashOfConfig": "199"}, {"size": 30462, "mtime": 1754807632899, "results": "233", "hashOfConfig": "199"}, {"size": 531, "mtime": 1747109267499, "results": "234", "hashOfConfig": "199"}, {"size": 23581, "mtime": 1753697060082, "results": "235", "hashOfConfig": "199"}, {"size": 10121, "mtime": 1747624459676, "results": "236", "hashOfConfig": "199"}, {"size": 6100, "mtime": 1747109267569, "results": "237", "hashOfConfig": "199"}, {"size": 354, "mtime": 1747109267605, "results": "238", "hashOfConfig": "199"}, {"size": 4841, "mtime": 1747109267602, "results": "239", "hashOfConfig": "199"}, {"size": 14852, "mtime": 1747109267661, "results": "240", "hashOfConfig": "199"}, {"size": 4990, "mtime": 1749722967811, "results": "241", "hashOfConfig": "199"}, {"size": 4724, "mtime": 1749722967814, "results": "242", "hashOfConfig": "199"}, {"size": 37540, "mtime": 1754972574122, "results": "243", "hashOfConfig": "199"}, {"size": 15866, "mtime": 1749486774681, "results": "244", "hashOfConfig": "199"}, {"size": 1070, "mtime": 1752489573454, "results": "245", "hashOfConfig": "199"}, {"size": 27384, "mtime": 1752489573456, "results": "246", "hashOfConfig": "199"}, {"size": 2942, "mtime": 1747289689132, "results": "247", "hashOfConfig": "199"}, {"size": 2594, "mtime": 1747109292536, "results": "248", "hashOfConfig": "199"}, {"size": 2908, "mtime": 1747624459651, "results": "249", "hashOfConfig": "199"}, {"size": 695, "mtime": 1749485471880, "results": "250", "hashOfConfig": "199"}, {"size": 4253, "mtime": 1747289688716, "results": "251", "hashOfConfig": "199"}, {"size": 9141, "mtime": 1747289688734, "results": "252", "hashOfConfig": "199"}, {"size": 5534, "mtime": 1751625232647, "results": "253", "hashOfConfig": "199"}, {"size": 41368, "mtime": 1754972574065, "results": "254", "hashOfConfig": "199"}, {"size": 1582, "mtime": 1747109267153, "results": "255", "hashOfConfig": "199"}, {"size": 3175, "mtime": 1747289688746, "results": "256", "hashOfConfig": "199"}, {"size": 18291, "mtime": 1747624459652, "results": "257", "hashOfConfig": "199"}, {"size": 9404, "mtime": 1754882479981, "results": "258", "hashOfConfig": "199"}, {"size": 6818, "mtime": 1747654911853, "results": "259", "hashOfConfig": "199"}, {"size": 2125, "mtime": 1747109268031, "results": "260", "hashOfConfig": "199"}, {"size": 4021, "mtime": 1747289689134, "results": "261", "hashOfConfig": "199"}, {"size": 1090, "mtime": 1747109268032, "results": "262", "hashOfConfig": "199"}, {"size": 1634, "mtime": 1747109268033, "results": "263", "hashOfConfig": "199"}, {"size": 2158, "mtime": 1747109268035, "results": "264", "hashOfConfig": "199"}, {"size": 6645, "mtime": 1748363529790, "results": "265", "hashOfConfig": "199"}, {"size": 2003, "mtime": 1747109268037, "results": "266", "hashOfConfig": "199"}, {"size": 1258, "mtime": 1747109268043, "results": "267", "hashOfConfig": "199"}, {"size": 833, "mtime": 1747289689135, "results": "268", "hashOfConfig": "199"}, {"size": 0, "mtime": 1744777321785, "results": "269", "hashOfConfig": "199"}, {"size": 1166, "mtime": 1747624459679, "results": "270", "hashOfConfig": "199"}, {"size": 3914, "mtime": 1747109268044, "results": "271", "hashOfConfig": "199"}, {"size": 8541, "mtime": 1747289689136, "results": "272", "hashOfConfig": "199"}, {"size": 3871, "mtime": 1747109268047, "results": "273", "hashOfConfig": "199"}, {"size": 992, "mtime": 1747109268048, "results": "274", "hashOfConfig": "199"}, {"size": 634, "mtime": 1747109268051, "results": "275", "hashOfConfig": "199"}, {"size": 4268, "mtime": 1753697061307, "results": "276", "hashOfConfig": "199"}, {"size": 2860, "mtime": 1747289689149, "results": "277", "hashOfConfig": "199"}, {"size": 1680, "mtime": 1747109268054, "results": "278", "hashOfConfig": "199"}, {"size": 750, "mtime": 1747109268056, "results": "279", "hashOfConfig": "199"}, {"size": 6382, "mtime": 1747109268057, "results": "280", "hashOfConfig": "199"}, {"size": 738, "mtime": 1747109268059, "results": "281", "hashOfConfig": "199"}, {"size": 4229, "mtime": 1747289689150, "results": "282", "hashOfConfig": "199"}, {"size": 22359, "mtime": 1747289689157, "results": "283", "hashOfConfig": "199"}, {"size": 292, "mtime": 1747109268060, "results": "284", "hashOfConfig": "199"}, {"size": 596, "mtime": 1753434897964, "results": "285", "hashOfConfig": "199"}, {"size": 2564, "mtime": 1747289689158, "results": "286", "hashOfConfig": "199"}, {"size": 2016, "mtime": 1747109268062, "results": "287", "hashOfConfig": "199"}, {"size": 781, "mtime": 1747109268063, "results": "288", "hashOfConfig": "199"}, {"size": 1952, "mtime": 1747289689159, "results": "289", "hashOfConfig": "199"}, {"size": 584, "mtime": 1749468144347, "results": "290", "hashOfConfig": "199"}, {"size": 7273, "mtime": 1747109268066, "results": "291", "hashOfConfig": "199"}, {"size": 1380, "mtime": 1754565812107, "results": "292", "hashOfConfig": "199"}, {"size": 188, "mtime": 1747109268072, "results": "293", "hashOfConfig": "199"}, {"size": 2293, "mtime": 1752465860247, "results": "294", "hashOfConfig": "199"}, {"size": 10374, "mtime": 1754882480280, "results": "295", "hashOfConfig": "199"}, {"size": 441, "mtime": 1753697061398, "results": "296", "hashOfConfig": "199"}, {"size": 1540, "mtime": 1754882480283, "results": "297", "hashOfConfig": "199"}, {"size": 310, "mtime": 1747109267096, "results": "298", "hashOfConfig": "199"}, {"size": 2032, "mtime": 1752465860837, "results": "299", "hashOfConfig": "199"}, {"size": 3382, "mtime": 1751255662795, "results": "300", "hashOfConfig": "199"}, {"size": 843, "mtime": 1747109292640, "results": "301", "hashOfConfig": "199"}, {"size": 1282, "mtime": 1751625233134, "results": "302", "hashOfConfig": "199"}, {"size": 2363, "mtime": 1748260878717, "results": "303", "hashOfConfig": "199"}, {"size": 1193, "mtime": 1748964660194, "results": "304", "hashOfConfig": "199"}, {"size": 438, "mtime": 1747109268089, "results": "305", "hashOfConfig": "199"}, {"size": 508, "mtime": 1747109268089, "results": "306", "hashOfConfig": "199"}, {"size": 1564, "mtime": 1747624459698, "results": "307", "hashOfConfig": "199"}, {"size": 2456, "mtime": 1754882480311, "results": "308", "hashOfConfig": "199"}, {"size": 3017, "mtime": 1747289689201, "results": "309", "hashOfConfig": "199"}, {"size": 1144, "mtime": 1747109268090, "results": "310", "hashOfConfig": "199"}, {"size": 414, "mtime": 1747109268091, "results": "311", "hashOfConfig": "199"}, {"size": 484, "mtime": 1747109268092, "results": "312", "hashOfConfig": "199"}, {"size": 590, "mtime": 1754125260516, "results": "313", "hashOfConfig": "199"}, {"size": 232, "mtime": 1747109268096, "results": "314", "hashOfConfig": "199"}, {"size": 1095, "mtime": 1747109268097, "results": "315", "hashOfConfig": "199"}, {"size": 1516, "mtime": 1750649654430, "results": "316", "hashOfConfig": "199"}, {"size": 1157, "mtime": 1752644594433, "results": "317", "hashOfConfig": "199"}, {"size": 458, "mtime": 1749201002646, "results": "318", "hashOfConfig": "199"}, {"size": 10094, "mtime": 1751024283211, "results": "319", "hashOfConfig": "199"}, {"size": 26618, "mtime": 1752465858731, "results": "320", "hashOfConfig": "199"}, {"size": 28459, "mtime": 1751625232977, "results": "321", "hashOfConfig": "199"}, {"size": 27181, "mtime": 1754882480151, "results": "322", "hashOfConfig": "199"}, {"size": 3193, "mtime": 1748962020700, "results": "323", "hashOfConfig": "199"}, {"size": 1643, "mtime": 1747624459680, "results": "324", "hashOfConfig": "199"}, {"size": 3539, "mtime": 1748260878719, "results": "325", "hashOfConfig": "199"}, {"size": 23652, "mtime": 1753697060968, "results": "326", "hashOfConfig": "199"}, {"size": 5982, "mtime": 1750070278915, "results": "327", "hashOfConfig": "199"}, {"size": 433, "mtime": 1749485552367, "results": "328", "hashOfConfig": "199"}, {"size": 466, "mtime": 1747797160907, "results": "329", "hashOfConfig": "199"}, {"size": 76070, "mtime": 1754972561310, "results": "330", "hashOfConfig": "199"}, {"size": 1825, "mtime": 1750070278881, "results": "331", "hashOfConfig": "199"}, {"size": 356, "mtime": 1747883078087, "results": "332", "hashOfConfig": "199"}, {"size": 4431, "mtime": 1753697061426, "results": "333", "hashOfConfig": "199"}, {"size": 2286, "mtime": 1753428052267, "results": "334", "hashOfConfig": "199"}, {"size": 16992, "mtime": 1751625232990, "results": "335", "hashOfConfig": "199"}, {"size": 17122, "mtime": 1748768935829, "results": "336", "hashOfConfig": "199"}, {"size": 977, "mtime": 1748768935828, "results": "337", "hashOfConfig": "199"}, {"size": 706, "mtime": 1748768935833, "results": "338", "hashOfConfig": "199"}, {"size": 523, "mtime": 1749201002641, "results": "339", "hashOfConfig": "199"}, {"size": 4913, "mtime": 1754896569445, "results": "340", "hashOfConfig": "199"}, {"size": 218, "mtime": 1754559296262, "results": "341", "hashOfConfig": "199"}, {"size": 1279, "mtime": 1749486774674, "results": "342", "hashOfConfig": "199"}, {"size": 41795, "mtime": 1754882480192, "results": "343", "hashOfConfig": "199"}, {"size": 962, "mtime": 1750070278918, "results": "344", "hashOfConfig": "199"}, {"size": 404, "mtime": 1749486774735, "results": "345", "hashOfConfig": "199"}, {"size": 598, "mtime": 1754882480314, "results": "346", "hashOfConfig": "199"}, {"size": 535, "mtime": 1749885108597, "results": "347", "hashOfConfig": "199"}, {"size": 2278, "mtime": 1749486774737, "results": "348", "hashOfConfig": "199"}, {"size": 460, "mtime": 1749486774737, "results": "349", "hashOfConfig": "199"}, {"size": 905, "mtime": 1752465858734, "results": "350", "hashOfConfig": "199"}, {"size": 2443, "mtime": 1751276652402, "results": "351", "hashOfConfig": "199"}, {"size": 58450, "mtime": 1753697058814, "results": "352", "hashOfConfig": "199"}, {"size": 1171, "mtime": 1751276652417, "results": "353", "hashOfConfig": "199"}, {"size": 8203, "mtime": 1752651150682, "results": "354", "hashOfConfig": "199"}, {"size": 535, "mtime": 1750649654200, "results": "355", "hashOfConfig": "199"}, {"size": 9056, "mtime": 1754882480252, "results": "356", "hashOfConfig": "199"}, {"size": 964, "mtime": 1751276652418, "results": "357", "hashOfConfig": "199"}, {"size": 841, "mtime": 1750649654428, "results": "358", "hashOfConfig": "199"}, {"size": 1293, "mtime": 1751625233132, "results": "359", "hashOfConfig": "199"}, {"size": 22034, "mtime": 1753697060075, "results": "360", "hashOfConfig": "199"}, {"size": 9983, "mtime": 1754463089905, "results": "361", "hashOfConfig": "199"}, {"size": 3543, "mtime": 1754882480305, "results": "362", "hashOfConfig": "199"}, {"size": 26853, "mtime": 1754882479994, "results": "363", "hashOfConfig": "199"}, {"size": 18050, "mtime": 1754972574078, "results": "364", "hashOfConfig": "199"}, {"size": 11685, "mtime": 1754882480090, "results": "365", "hashOfConfig": "199"}, {"size": 53985, "mtime": 1754882480104, "results": "366", "hashOfConfig": "199"}, {"size": 13760, "mtime": 1752465859165, "results": "367", "hashOfConfig": "199"}, {"size": 14090, "mtime": 1752465859848, "results": "368", "hashOfConfig": "199"}, {"size": 1341, "mtime": 1754882480266, "results": "369", "hashOfConfig": "199"}, {"size": 1051, "mtime": 1752465859906, "results": "370", "hashOfConfig": "199"}, {"size": 1196, "mtime": 1752465859911, "results": "371", "hashOfConfig": "199"}, {"size": 1670, "mtime": 1753697061390, "results": "372", "hashOfConfig": "199"}, {"size": 1853, "mtime": 1754882480285, "results": "373", "hashOfConfig": "199"}, {"size": 1340, "mtime": 1754882480301, "results": "374", "hashOfConfig": "199"}, {"size": 2091, "mtime": 1754882480302, "results": "375", "hashOfConfig": "199"}, {"size": 951, "mtime": 1752465862526, "results": "376", "hashOfConfig": "199"}, {"size": 941, "mtime": 1752465862528, "results": "377", "hashOfConfig": "199"}, {"size": 16670, "mtime": 1754972574107, "results": "378", "hashOfConfig": "199"}, {"size": 349, "mtime": 1754882480164, "results": "379", "hashOfConfig": "199"}, {"size": 2404, "mtime": 1753697060547, "results": "380", "hashOfConfig": "199"}, {"size": 1564, "mtime": 1754882480308, "results": "381", "hashOfConfig": "199"}, {"size": 1354, "mtime": 1753697061415, "results": "382", "hashOfConfig": "199"}, {"size": 20095, "mtime": 1754882480213, "results": "383", "hashOfConfig": "199"}, {"size": 343, "mtime": 1754882480039, "results": "384", "hashOfConfig": "199"}, {"size": 9420, "mtime": 1754972574230, "results": "385", "hashOfConfig": "199"}, {"size": 2840, "mtime": 1754882480299, "results": "386", "hashOfConfig": "199"}, {"size": 13125, "mtime": 1754882480043, "results": "387", "hashOfConfig": "199"}, {"size": 36506, "mtime": 1754882480037, "results": "388", "hashOfConfig": "199"}, {"size": 7129, "mtime": 1754882480120, "results": "389", "hashOfConfig": "199"}, {"size": 23244, "mtime": 1754882480125, "results": "390", "hashOfConfig": "199"}, {"size": 28011, "mtime": 1754896569448, "results": "391", "hashOfConfig": "199"}, {"size": 783, "mtime": 1754896569449, "results": "392", "hashOfConfig": "199"}, {"size": 5004, "mtime": 1754972574170, "results": "393", "hashOfConfig": "199"}, {"size": 3629, "mtime": 1754972574118, "results": "394", "hashOfConfig": "199"}, {"size": 21388, "mtime": 1754989164407, "results": "395", "hashOfConfig": "199"}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uj1650", {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\blogs\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\apply\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\careers\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\components\\thoughtSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\certificates-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\certificates\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\components\\sidebar-nav.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\description-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\description\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\education-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\education\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\experience-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\experience\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\photo-and-logo\\photo-and-logo.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\profile-form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\tution-class\\setup-tution-class.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes-details\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\coins\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\page.tsx", ["987"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\privacy-policy\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\wishlist\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\FAQSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\support\\SupportOptions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\terms-and-conditions\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\countDownTimer.tsx", ["988"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\examStatusButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-details\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\FilterInput.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verified-classes\\page.tsx", [], ["989"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-email\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AppDatePicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthActions.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\AuthErrorHandler.tsx", [], ["990"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\BlogCard.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\DynamicTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Footer.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\Header.tsx", ["991"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\MonthYearPicker.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\RecentBlogs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ReviewsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SignUpSignIn.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\TestimonialSlider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\collapsible.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\command.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\CustomModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\popover.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\progress.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\textarea.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\useFullScreen.ts", [], ["992"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\constant\\quizConstant.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\helper.ts", [], ["993"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\useAuth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\Providers\\theme-provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\AuthService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\careerService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizAttemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentAuthServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentWishlistServices.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizCertificateApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizRankingApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\index.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\provider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\classSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\formProgressSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\userSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\classThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\blogs\\add\\AddBlogPageContent.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\class\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\question-bank\\page.tsx", [], ["994", "995"], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\StatsSection.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\alert.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-info\\[examid]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\GoogleLoginButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\AnalyticsProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\lib\\gtag.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\ProfileCompletionIndicator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\hooks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\slices\\studentProfileSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\store\\thunks\\studentProfileThunks.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\referral-dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\referralApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examApplicantEmailApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExamButton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\restictExamAttempt.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-exam\\[examId]\\page.tsx", ["996", "997", "998", "999", "1000", "1001", "1002"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\quizTeminationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\studentDetailServiceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizPreventReattemptApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizQuestionForStudentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizSaveExamAnswerApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\SharedChat.tsx", ["1003"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\chatService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\AddressForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\profile\\address\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ExamCameraMonitoring.tsx", ["1004"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\classViewLogService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\PosterDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\notifications\\page.tsx", ["1005"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app-components\\NotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\notificationService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\Leader-Board\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-card\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-exam-result\\[studentId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\mockExam.tsx", ["1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student-verify-otp\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\verify-otp\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\badgedisplay.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\streakcountdisplay.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\ui\\totaluestcoin.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\hooks\\getStudentId.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\LeaderboardUserApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mock-exam-resultApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\mockExamStreakApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uestCoinTransctionApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\uwhizMockExamTerminationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\my-orders\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\profile\\components\\sidebar-nav.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\storeApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\storePurchaseApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\uwhiz-super-kids-result\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\my-orders\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\MyOrders.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\cartApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\classes\\payment\\page.tsx", ["1016"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\about\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\mock-test\\weeklyExam.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\store\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\weekly-exam-result\\page.tsx", ["1017"], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\services\\WeeklyExamService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\weekly-exam-card\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\app\\student\\pricing\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\client\\src\\components\\SpinningWheel.tsx", ["1018"], [], {"ruleId": "1019", "severity": 1, "message": "1020", "line": 227, "column": 6, "nodeType": "1021", "endLine": 227, "endColumn": 8, "suggestions": "1022"}, {"ruleId": "1019", "severity": 1, "message": "1023", "line": 100, "column": 6, "nodeType": "1021", "endLine": 100, "endColumn": 62, "suggestions": "1024"}, {"ruleId": "1019", "severity": 1, "message": "1025", "line": 258, "column": 6, "nodeType": "1021", "endLine": 258, "endColumn": 47, "suggestions": "1026", "suppressions": "1027"}, {"ruleId": "1019", "severity": 1, "message": "1028", "line": 21, "column": 6, "nodeType": "1021", "endLine": 21, "endColumn": 17, "suggestions": "1029", "suppressions": "1030"}, {"ruleId": "1019", "severity": 1, "message": "1031", "line": 156, "column": 6, "nodeType": "1021", "endLine": 156, "endColumn": 16, "suggestions": "1032"}, {"ruleId": "1019", "severity": 1, "message": "1033", "line": 193, "column": 6, "nodeType": "1021", "endLine": 202, "endColumn": 4, "suggestions": "1034", "suppressions": "1035"}, {"ruleId": "1036", "severity": 2, "message": "1037", "line": 44, "column": 15, "nodeType": "1038", "messageId": "1039", "endLine": 44, "endColumn": 22, "suppressions": "1040"}, {"ruleId": "1019", "severity": 1, "message": "1041", "line": 206, "column": 6, "nodeType": "1021", "endLine": 206, "endColumn": 8, "suggestions": "1042", "suppressions": "1043"}, {"ruleId": "1019", "severity": 1, "message": "1044", "line": 211, "column": 6, "nodeType": "1021", "endLine": 211, "endColumn": 42, "suggestions": "1045", "suppressions": "1046"}, {"ruleId": "1019", "severity": 1, "message": "1047", "line": 111, "column": 6, "nodeType": "1021", "endLine": 111, "endColumn": 28, "suggestions": "1048"}, {"ruleId": "1019", "severity": 1, "message": "1049", "line": 206, "column": 6, "nodeType": "1021", "endLine": 206, "endColumn": 77, "suggestions": "1050"}, {"ruleId": "1019", "severity": 1, "message": "1051", "line": 295, "column": 6, "nodeType": "1021", "endLine": 295, "endColumn": 43, "suggestions": "1052"}, {"ruleId": "1019", "severity": 1, "message": "1053", "line": 316, "column": 6, "nodeType": "1021", "endLine": 316, "endColumn": 132, "suggestions": "1054"}, {"ruleId": "1019", "severity": 1, "message": "1055", "line": 361, "column": 6, "nodeType": "1021", "endLine": 361, "endColumn": 45, "suggestions": "1056"}, {"ruleId": "1019", "severity": 1, "message": "1055", "line": 370, "column": 6, "nodeType": "1021", "endLine": 370, "endColumn": 45, "suggestions": "1057"}, {"ruleId": "1019", "severity": 1, "message": "1058", "line": 533, "column": 5, "nodeType": "1021", "endLine": 533, "endColumn": 112, "suggestions": "1059"}, {"ruleId": "1019", "severity": 1, "message": "1060", "line": 228, "column": 8, "nodeType": "1021", "endLine": 228, "endColumn": 82, "suggestions": "1061"}, {"ruleId": "1019", "severity": 1, "message": "1062", "line": 169, "column": 6, "nodeType": "1021", "endLine": 169, "endColumn": 91, "suggestions": "1063"}, {"ruleId": "1019", "severity": 1, "message": "1064", "line": 150, "column": 6, "nodeType": "1021", "endLine": 150, "endColumn": 16, "suggestions": "1065"}, {"ruleId": "1019", "severity": 1, "message": "1047", "line": 123, "column": 6, "nodeType": "1021", "endLine": 123, "endColumn": 17, "suggestions": "1066"}, {"ruleId": "1019", "severity": 1, "message": "1067", "line": 180, "column": 6, "nodeType": "1021", "endLine": 180, "endColumn": 65, "suggestions": "1068"}, {"ruleId": "1019", "severity": 1, "message": "1069", "line": 347, "column": 6, "nodeType": "1021", "endLine": 347, "endColumn": 60, "suggestions": "1070"}, {"ruleId": "1019", "severity": 1, "message": "1055", "line": 474, "column": 6, "nodeType": "1021", "endLine": 474, "endColumn": 34, "suggestions": "1071"}, {"ruleId": "1019", "severity": 1, "message": "1055", "line": 482, "column": 6, "nodeType": "1021", "endLine": 482, "endColumn": 34, "suggestions": "1072"}, {"ruleId": "1019", "severity": 1, "message": "1073", "line": 544, "column": 6, "nodeType": "1021", "endLine": 544, "endColumn": 174, "suggestions": "1074"}, {"ruleId": "1019", "severity": 1, "message": "1073", "line": 609, "column": 5, "nodeType": "1021", "endLine": 609, "endColumn": 173, "suggestions": "1075"}, {"ruleId": "1019", "severity": 1, "message": "1073", "line": 649, "column": 6, "nodeType": "1021", "endLine": 649, "endColumn": 174, "suggestions": "1076"}, {"ruleId": "1019", "severity": 1, "message": "1073", "line": 691, "column": 6, "nodeType": "1021", "endLine": 691, "endColumn": 174, "suggestions": "1077"}, {"ruleId": "1019", "severity": 1, "message": "1073", "line": 730, "column": 6, "nodeType": "1021", "endLine": 730, "endColumn": 174, "suggestions": "1078"}, {"ruleId": "1019", "severity": 1, "message": "1079", "line": 121, "column": 6, "nodeType": "1021", "endLine": 121, "endColumn": 8, "suggestions": "1080"}, {"ruleId": "1019", "severity": 1, "message": "1081", "line": 120, "column": 8, "nodeType": "1021", "endLine": 120, "endColumn": 24, "suggestions": "1082"}, {"ruleId": "1019", "severity": 1, "message": "1083", "line": 132, "column": 6, "nodeType": "1021", "endLine": 132, "endColumn": 12, "suggestions": "1084"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", "ArrayExpression", ["1085"], "React Hook useEffect has a missing dependency: 'exam.duration'. Either include it or remove the dependency array.", ["1086"], "React Hook useEffect has missing dependencies: 'fetchNearbyTutors' and 'fetchTutors'. Either include them or remove the dependency array.", ["1087"], ["1088"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["1089"], ["1090"], "React Hook useEffect has missing dependencies: 'isAuthenticated' and 'user.id'. Either include them or remove the dependency array.", ["1091"], "React Hook useEffect has missing dependencies: 'enterFullscreen' and 'isFullscreen'. Either include them or remove the dependency array.", ["1092"], ["1093"], "prefer-const", "'minutes' is never reassigned. Use 'const' instead.", "Identifier", "useConst", ["1094"], "React Hook useEffect has a missing dependency: 'fetchConstants'. Either include it or remove the dependency array.", ["1095"], ["1096"], "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1097"], ["1098"], "React Hook useEffect has a missing dependency: 'initializeViolationCounts'. Either include it or remove the dependency array.", ["1099"], "React Hook useCallback has a missing dependency: 'isSubmitted'. Either include it or remove the dependency array.", ["1100"], "React Hook useCallback has an unnecessary dependency: 'isCameraReady'. Either exclude it or remove the dependency array.", ["1101"], "React Hook useEffect has a missing dependency: 'userAnswers'. Either include it or remove the dependency array.", ["1102"], "React Hook useEffect has a missing dependency: 'exitFullScreen'. Either include it or remove the dependency array.", ["1103"], ["1104"], "React Hook useCallback has unnecessary dependencies: 'examIdStr' and 'studentId'. Either exclude them or remove the dependency array.", ["1105"], "React Hook useEffect has missing dependencies: 'currentRoomId', 'offlineMessageUsers', 'selectedUserId', 'socketPath', and 'socketUrl'. Either include them or remove the dependency array.", ["1106"], "React Hook useEffect has a missing dependency: 'isFaceApiReady'. Either include it or remove the dependency array.", ["1107"], "React Hook useCallback has a missing dependency: 'user'. Either include it or remove the dependency array.", ["1108"], ["1109"], "React Hook useEffect has a missing dependency: 'saveResult'. Either include it or remove the dependency array.", ["1110"], "React Hook useEffect has missing dependencies: 'exitFullScreen', 'hasSavedResult', and 'saveResult'. Either include them or remove the dependency array.", ["1111"], ["1112"], ["1113"], "React Hook useCallback has a missing dependency: 'saveResult'. Either include it or remove the dependency array.", ["1114"], ["1115"], ["1116"], ["1117"], ["1118"], "React Hook useEffect has a missing dependency: 'fetchBankPaymentDetails'. Either include it or remove the dependency array.", ["1119"], "React Hook useEffect has a missing dependency: 'fetchWeeklyResult'. Either include it or remove the dependency array.", ["1120"], "React Hook useEffect has a missing dependency: 'checkSpinEligibility'. Either include it or remove the dependency array.", ["1121"], {"desc": "1122", "fix": "1123"}, {"desc": "1124", "fix": "1125"}, {"desc": "1126", "fix": "1127"}, {"kind": "1128", "justification": "1129"}, {"desc": "1130", "fix": "1131"}, {"kind": "1128", "justification": "1129"}, {"desc": "1132", "fix": "1133"}, {"desc": "1134", "fix": "1135"}, {"kind": "1128", "justification": "1129"}, {"kind": "1128", "justification": "1129"}, {"desc": "1136", "fix": "1137"}, {"kind": "1128", "justification": "1129"}, {"desc": "1138", "fix": "1139"}, {"kind": "1128", "justification": "1129"}, {"desc": "1140", "fix": "1141"}, {"desc": "1142", "fix": "1143"}, {"desc": "1144", "fix": "1145"}, {"desc": "1146", "fix": "1147"}, {"desc": "1148", "fix": "1149"}, {"desc": "1150", "fix": "1151"}, {"desc": "1152", "fix": "1153"}, {"desc": "1154", "fix": "1155"}, {"desc": "1156", "fix": "1157"}, {"desc": "1158", "fix": "1159"}, {"desc": "1160", "fix": "1161"}, {"desc": "1162", "fix": "1163"}, {"desc": "1164", "fix": "1165"}, {"desc": "1166", "fix": "1167"}, {"desc": "1168", "fix": "1169"}, {"desc": "1170", "fix": "1171"}, {"desc": "1170", "fix": "1172"}, {"desc": "1170", "fix": "1173"}, {"desc": "1170", "fix": "1174"}, {"desc": "1170", "fix": "1175"}, {"desc": "1176", "fix": "1177"}, {"desc": "1178", "fix": "1179"}, {"desc": "1180", "fix": "1181"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1182", "text": "1183"}, "Update the dependencies array to be: [exam.start_date, exam.start_registration_date, exam.id, exam.duration]", {"range": "1184", "text": "1185"}, "Update the dependencies array to be: [page, useNearby, userLocation, distance, fetchNearbyTutors, fetchTutors]", {"range": "1186", "text": "1187"}, "directive", "", "Update the dependencies array to be: [authError, dispatch]", {"range": "1188", "text": "1189"}, "Update the dependencies array to be: [dispatch, isAuthenticated, user.id]", {"range": "1190", "text": "1191"}, "Update the dependencies array to be: [escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", {"range": "1192", "text": "1193"}, "Update the dependencies array to be: [fetchConstants]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [currentPage, limit, filtersApplied, fetchQuestions]", {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [studentId, examIdStr, initializeViolationCounts]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [isSubmitted, studentId, examIdStr, selectedAnswer, currentQuestionIndex, questions]", {"range": "1200", "text": "1201"}, "Update the dependencies array to be: [studentId, examIdStr]", {"range": "1202", "text": "1203"}, "Update the dependencies array to be: [currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", {"range": "1204", "text": "1205"}, "Update the dependencies array to be: [isQuizCompleted, studentId, examIdStr, exitFullScreen]", {"range": "1206", "text": "1207"}, "Update the dependencies array to be: [showTermination, studentId, examIdStr, exitFullScreen]", {"range": "1208", "text": "1209"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", {"range": "1210", "text": "1211"}, "Update the dependencies array to be: [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, socketUrl, socketPath, selectedUserId, offlineMessageUsers, currentRoomId]", {"range": "1212", "text": "1213"}, "Update the dependencies array to be: [studentId, isExamActive, isCameraActive, onCameraError, onCameraStatus, onViolation, isFaceApiReady]", {"range": "1214", "text": "1215"}, "Update the dependencies array to be: [user, userType]", {"range": "1216", "text": "1217"}, "Update the dependencies array to be: [initializeViolationCounts, studentId]", {"range": "1218", "text": "1219"}, "Update the dependencies array to be: [violationCount, studentId, calculateCoins, hasSavedResult, saveResult]", {"range": "1220", "text": "1221"}, "Update the dependencies array to be: [isQuizCompleted, studentId, calculateCoins, isWeekly, hasSavedResult, saveResult, exitFullScreen]", {"range": "1222", "text": "1223"}, "Update the dependencies array to be: [exitFullScreen, isQuizCompleted, studentId]", {"range": "1224", "text": "1225"}, "Update the dependencies array to be: [exitFullScreen, showTermination, studentId]", {"range": "1226", "text": "1227"}, "Update the dependencies array to be: [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen, isQuizCompleted, studentId, hasSavedResult, saveResult, calculateCoins]", {"range": "1228", "text": "1229"}, {"range": "1230", "text": "1229"}, {"range": "1231", "text": "1229"}, {"range": "1232", "text": "1229"}, {"range": "1233", "text": "1229"}, "Update the dependencies array to be: [fetchBankPaymentDetails]", {"range": "1234", "text": "1235"}, "Update the dependencies array to be: [fetchWeeklyResult, selectedSunday]", {"range": "1236", "text": "1237"}, "Update the dependencies array to be: [checkSpinEligibility, user]", {"range": "1238", "text": "1239"}, [7064, 7066], "[fetchCategories]", [3832, 3888], "[exam.start_date, exam.start_registration_date, exam.id, exam.duration]", [7250, 7291], "[page, useNearby, userLocation, distance, fetchNearbyTutors, fetchTutors]", [622, 633], "[auth<PERSON><PERSON><PERSON>, dispatch]", [5021, 5031], "[dispatch, isAuthenticated, user.id]", [6888, 7055], "[escAttempts, showWarningDialog, showTerminationDialog, lastViolationTime, isExitingForSubmit, classId, examId, isMobile, isFullscreen, enterFullscreen]", [7258, 7260], "[fetchConstants]", [7403, 7439], "[currentPage, limit, filtersApplied, fetchQuestions]", [4144, 4166], "[studentId, examIdStr, initializeViolationCounts]", [7140, 7211], "[isSubmitted, studentId, examIdStr, selectedAnswer, currentQuestionIndex, questions]", [10634, 10671], "[studentId, examIdStr]", [11375, 11501], "[currentQuestionIndex, questions, studentId, examIdStr, isDialogOpen, showTermination, isLoginDialogOpen, isProfileDialogOpen, userAnswers]", [13376, 13415], "[isQuizCompleted, studentId, examIdStr, exitFullScreen]", [13706, 13745], "[showTermination, studentId, examIdStr, exitFullScreen]", [20439, 20546], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending]", [9625, 9699], "[username, isUsernameSet, isAuthenticated, userType, userId, selectedUser, socketUrl, socketPath, selectedUserId, offlineMessageUsers, currentRoomId]", [5746, 5831], "[studentId, isExamActive, isCameraActive, onCameraError, onCameraStatus, onViolation, isFaceApiReady]", [5157, 5167], "[user, userType]", [4961, 4972], "[initializeViolationCounts, studentId]", [6884, 6943], "[violationCount, studentId, calculate<PERSON><PERSON><PERSON>, hasSavedResult, saveResult]", [12921, 12975], "[isQuizCompleted, studentId, calculate<PERSON>oi<PERSON>, isWeekly, hasSavedResult, saveResult, exitFullScreen]", [17613, 17641], "[exitFullScreen, isQuizCompleted, studentId]", [17850, 17878], "[exitFullScreen, showTermination, studentId]", [20015, 20183], "[isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showWarning, isApiCallPending, isExamTakenDialogOpen, isQuizCompleted, studentId, hasSavedResult, saveResult, calculateCoins]", [22884, 23052], [24603, 24771], [26605, 26773], [28364, 28532], [3294, 3296], "[fetchBankPaymentDetails]", [4366, 4382], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>]", [5243, 5249], "[checkSpinEligibility, user]"]