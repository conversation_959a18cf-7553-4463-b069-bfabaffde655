'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { ArrowLeft, Loader2, Camera, Upload, X, FileText } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { updateStudentByAdmin } from '@/services/studentApi';
import Image from 'next/image';
import { axiosInstance } from '@/lib/axios';

const profileFormSchema = z.object({
    firstName: z
        .string()
        .min(2, 'First name must be at least 2 characters')
        .optional(),
    middleName: z
        .string()
        .optional(),
    lastName: z
        .string()
        .min(2, 'Last name must be at least 2 characters')
        .optional(),
    contact: z.string()
        .min(10, 'Contact number must be at least 10 digits')
        .regex(/^\d+$/, 'Contact number must contain only digits')
        .optional(),
    contactNo2: z.string()
        .min(10, 'Contact number must be at least 10 digits')
        .regex(/^\d+$/, 'Contact number must contain only digits')
        .optional()
        .or(z.literal('')),
    medium: z.string().min(1, 'Medium is required').optional(),
    classroom: z.string().min(1, 'Class is required').optional(),
    birthday: z.date().optional(),
    school: z.string().min(2, 'School name must be at least 2 characters').optional(),
    bloodGroup: z.string().optional(),
    // age: z.string().optional(),
    email: z.string().email('Invalid email address').optional(),
    gender: z.string().optional(),
    address: z.string().min(5, 'Address must be at least 5 characters').optional(),
    aadhaarNo: z.string()
        .min(12, 'Aadhaar number must be 12 digits')
        .max(12, 'Aadhaar number must be 12 digits')
        .regex(/^\d+$/, 'Aadhaar number must contain only digits')
        .optional(),
    birthPlace: z.string().optional(),
    motherTongue: z.string().optional(),
    religion: z.string().optional(),
    caste: z.string().optional(),
    subCaste: z.string().optional(),
    photo: z.any().optional(),
    document: z.any().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;
type DocumentType = File | { name: string; size: number; url: string; type: string };

const StudentEditPage = () => {
    const params = useParams();
    const router = useRouter();
    const studentId = params.id as string;

    const [isLoading, setIsLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const [photo, setPhoto] = useState<string | null>(null);
    const [uploadedPhoto, setUploadedPhoto] = useState<File | null>(null);
    const [uploadedDocument, setUploadedDocument] = useState<DocumentType | null>(null);
    const [classroomOptions, setClassroomOptions] = useState<Array<{id: number, value: string}>>([]);
    const [mediumOptions] = useState([
        { id: 1, value: 'english' },
        { id: 2, value: 'gujarati' }
    ]);

    const form = useForm<ProfileFormValues>({
        resolver: zodResolver(profileFormSchema),
        defaultValues: {
            firstName: '',
            middleName: '',
            lastName: '',
            contact: '',
            contactNo2: '',
            medium: '',
            email: '',
            gender: '',
            classroom: '',
            birthday: undefined,
            school: '',
            address: '',
            bloodGroup: '',
            aadhaarNo: '',
            birthPlace: '',
            motherTongue: '',
            religion: '',
            caste: '',
            subCaste: ''
        },
        mode: 'onSubmit',
    });

    const fetchStudentData = useCallback(async () => {
        try {
            setIsLoading(true);

            const response = await axiosInstance.get(`/student-profile/admin/${studentId}/all-data`);
            console.log("aaaaaaaaa",response.data.data);
            const allData = response.data.data;
            const profileData = allData.profile;

            // Set classroom options - use provided options or fetch constants
            const classroomOpts = allData.classroomOptions || [];
            if (classroomOpts.length > 0) {
                setClassroomOptions(classroomOpts);
            } else {
                const constantsResponse = await axiosInstance.get('/constant/classroom');
                const formattedOptions = constantsResponse.data.details?.map((detail: any, index: number) => ({
                    id: detail.id || index + 1,
                    value: detail.value
                })) || [];
                setClassroomOptions(formattedOptions);
            }

            // Set form values
            const formValues = {
                firstName: profileData.student?.firstName || '',
                middleName: profileData.student?.middleName || '',
                lastName: profileData.student?.lastName || '',
                contact: profileData.student?.contact || '',
                contactNo2: profileData.contactNo2 || '',
                medium: profileData.medium || '',
                classroom: profileData.classroom || '',
                email: profileData.student?.email || '',
                gender: profileData.gender || '',
                school: profileData.school || '',
                bloodGroup: profileData.bloodGroup || '',
                age: profileData.age || '',
                address: profileData.address || '',
                birthday: profileData.birthday ? new Date(profileData.birthday) : undefined,
                aadhaarNo: profileData.aadhaarNo || '',
                birthPlace: profileData.birthPlace || '',
                motherTongue: profileData.motherTongue || '',
                religion: profileData.religion || '',
                caste: profileData.caste || '',
                subCaste: profileData.subCaste || ''
            };

            Object.entries(formValues).forEach(([key, value]) => {
                form.setValue(key as any, value);
            });

            // Set photo
            if (profileData.photo) {
                setPhoto(profileData.photo);
            }

            // Set document
            if (profileData.documentUrl) {
                const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';
                const documentUrl = profileData.documentUrl.startsWith('http')
                    ? profileData.documentUrl
                    : `${baseUrl}${profileData.documentUrl}`;

                setUploadedDocument({
                    name: documentUrl.split('/').pop() || 'Uploaded Document',
                    size: 0,
                    url: documentUrl,
                    type: 'application/octet-stream'
                });
            }

        } catch (error) {
            console.error('Failed to fetch student data:', error);
            toast.error('Failed to load student data');
        } finally {
            setIsLoading(false);
        }
    }, [studentId, form]);

    useEffect(() => {
        fetchStudentData();
    }, [fetchStudentData]);



    const formatFileSize = (bytes: number) => bytes < 1048576 ? (bytes / 1024).toFixed(1) + ' KB' : (bytes / 1048576).toFixed(1) + ' MB';

    const handlePhotoUpload = (file: File) => {
        if (file.size > 5 * 1024 * 1024) {
            form.setError('photo', { message: 'Photo size exceeds 5MB limit' });
            return;
        }
        form.clearErrors('photo');
        setUploadedPhoto(file);
        form.setValue('photo', file);
        const reader = new FileReader();
        reader.onload = (e) => setPhoto(e.target?.result as string);
        reader.readAsDataURL(file);
    };

    const onSubmit = async (data: ProfileFormValues) => {
        try {
            setIsSaving(true);

            console.log('Form data received:', data);
            console.log('Form errors:', form.formState.errors);

            const jsonData: any = {
                firstName: data.firstName,
                middleName: data.middleName,
                lastName: data.lastName,
                contact: data.contact,
                contact2: data.contactNo2,
                medium: data.medium,
                classroom: data.classroom,
                email: data.email,
                gender: data.gender,
                birthday: data.birthday ? data.birthday.toISOString() : undefined,
                school: data.school,
                address: data.address,
                bloodGroup: data.bloodGroup,
                // age: data.age ? String(data.age) : undefined,
                aadhaarNumber: data.aadhaarNo, 
                birthPlace: data.birthPlace,
                motherTongue: data.motherTongue,
                religion: data.religion,
                caste: data.caste,
                subCaste: data.subCaste
            };

            // Handle photo upload
            if (uploadedPhoto instanceof File && uploadedPhoto.size > 0) {
                jsonData.photo = await fileToBase64(uploadedPhoto);
                jsonData.photoMimeType = uploadedPhoto.type;
            }

            // Handle document upload
            if (uploadedDocument instanceof File && uploadedDocument.size > 0) {
                jsonData.document = await fileToBase64(uploadedDocument);
                jsonData.documentMimeType = uploadedDocument.type;
                jsonData.documentName = uploadedDocument.name;
            }

            // Remove undefined, null, and empty string values
            Object.keys(jsonData).forEach(key => {
                if (jsonData[key] === undefined || jsonData[key] === null || jsonData[key] === '') {
                    delete jsonData[key];
                }
            });

            // Ensure required fields have valid values
            if (jsonData.birthday && typeof jsonData.birthday === 'string') {
                // Make sure birthday is a valid date string
                const date = new Date(jsonData.birthday);
                if (isNaN(date.getTime())) {
                    delete jsonData.birthday;
                }
            }

            console.log('Sending data:', jsonData);
            await updateStudentByAdmin(studentId, jsonData);
            toast.success('Student updated successfully!');
            // router.push('/student-details');
        } catch (error: any) {
            console.error('Failed to update student:', error);
            console.error('Error response:', error.response?.data);
            console.error('Error status:', error.response?.status);
            toast.error(error.response?.data?.message || error.message || 'Failed to update student');
        } finally {
            setIsSaving(false);
        }
    };

    const fileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const result = reader.result as string;
                const base64 = result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = error => reject(error);
        });
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
        );
    }

    return (
        <div className="container mx-auto py-6 px-4 max-w-6xl">
            <div className="flex items-center gap-4 mb-6">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => router.back()}
                    className="hover:bg-gray-100"
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-2xl font-bold">Edit Student</h1>
            </div>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-medium">Personal Information</CardTitle>
                            <CardDescription>Update student personal details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <FormField
                                    control={form.control}
                                    name="firstName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">First Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter first name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="middleName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Middle Name</FormLabel>
                                            <FormControl>   
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter last name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="lastName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Last Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter last name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>

                           
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Email</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter contact number"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="gender"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Gender</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value || undefined}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                                                        <SelectValue placeholder="Select" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                                                    <SelectItem value="male">Male</SelectItem>
                                                    <SelectItem value="female">Female</SelectItem>
                                                    <SelectItem value="other">Other</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormField
                                    control={form.control}
                                    name="contact"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Contact Number</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter contact number"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="contactNo2"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Another Contact Number </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter contact number"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-medium">Profile Information</CardTitle>
                            <CardDescription>Update student profile details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormField
                                    control={form.control}
                                    name="medium"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Medium</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg">
                                                        <SelectValue placeholder="Select medium" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {mediumOptions.map((option) => (
                                                        <SelectItem key={option.id} value={option.value}>
                                                            {option.value.charAt(0).toUpperCase() + option.value.slice(1)}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="classroom"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Class</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg">
                                                        <SelectValue placeholder="Select class" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {classroomOptions.length > 0 ? (
                                                        classroomOptions.map((option) => (
                                                            <SelectItem key={option.id} value={option.value}>
                                                                {option.value}
                                                            </SelectItem>
                                                        ))
                                                    ) : (
                                                        <div className="p-2 text-center text-gray-500">
                                                            No classroom options available
                                                        </div>
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="birthday"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Birthday</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="date"
                                                    value={field.value ? (() => {
                                                        const date = new Date(field.value);
                                                        const year = date.getFullYear();
                                                        const month = String(date.getMonth() + 1).padStart(2, '0');
                                                        const day = String(date.getDate()).padStart(2, '0');
                                                        return `${year}-${month}-${day}`;
                                                    })() : ''}
                                                    onChange={(e) => {
                                                        if (e.target.value) {
                                                            const date = new Date(e.target.value + 'T00:00:00');
                                                            field.onChange(date);
                                                        } else {
                                                            field.onChange(undefined);
                                                        }
                                                    }}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="bloodGroup"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Blood Group</FormLabel>
                                            <Select
                                                onValueChange={field.onChange}
                                                value={field.value || ""}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                                                        <SelectValue placeholder="Select Blood Group" />
                                                    </SelectTrigger>
                                                </FormControl>

                                                <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                                                    {["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"].map((group) => (
                                                        <SelectItem key={group} value={group}>
                                                            {group}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                 <FormField
                                    control={form.control}
                                    name="school"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">School Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter school name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="address"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-black font-medium">Address</FormLabel>
                                        <FormControl>
                                            <Input
                                                {...field}
                                                className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                placeholder="Enter address"
                                            />
                                        </FormControl>
                                        <FormMessage className="text-red-500" />
                                    </FormItem>
                                )}
                            />
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormField
                                    control={form.control}
                                    name="aadhaarNo"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Aadhar card Number</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter address"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="birthPlace"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Birthplace</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter "
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="motherTongue"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Mother Tongue</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter address"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="religion"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Religion</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter address"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="caste"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Caste</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter address"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="subCaste"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Sub Caste</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter address"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Photo Upload */}
                    <FormField
                        control={form.control}
                        name="photo"
                        render={({ fieldState }) => (
                            <Card className={`shadow-sm ${fieldState.error ? 'border-red-500 border-2' : ''}`}>
                                <CardHeader>
                                    <CardTitle className="text-lg font-medium">Profile Photo</CardTitle>
                                    <CardDescription>Upload or update student profile photo</CardDescription>
                                </CardHeader>
                                <CardContent>
                            <div className="space-y-4">
                                {/* Photo Display */}
                                {(uploadedPhoto || photo) ? (
                                    <div className="flex justify-center">
                                        <div className="border rounded-lg shadow-md bg-gray-50 p-4 max-w-full">
                                            <div className="flex justify-center">
                                                <Image
                                                    src={uploadedPhoto ? URL.createObjectURL(uploadedPhoto) :
                                                         photo?.startsWith('http') ? photo :
                                                         `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${photo}?t=${new Date().getTime()}`}
                                                    alt="Student Photo"
                                                    className="max-w-full max-h-96 object-contain rounded-lg"
                                                    height={1000}
                                                    width={1000}
                                                    style={{ height: 'auto', width: 'auto' }}
                                                    onError={(e) => e.currentTarget.style.display = 'none'}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center h-36 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                                        <div className="text-center">
                                            <Camera className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                                            <p className="text-sm text-gray-500">No photo available</p>
                                        </div>
                                    </div>
                                )}

                                {/* Upload Controls */}
                                <div className="flex justify-center space-x-2">
                                    <label>
                                        <Button type="button" variant="outline" size="sm" asChild>
                                            <span>{(uploadedPhoto || photo) ? 'Change Photo' : 'Upload Photo'}</span>
                                        </Button>
                                        <Input
                                            type="file"
                                            accept=".jpg,.jpeg,.png"
                                            className="hidden"
                                            onChange={(e) => {
                                                const file = e.target.files?.[0];
                                                if (file) handlePhotoUpload(file);
                                            }}
                                        />
                                    </label>
                                    {(uploadedPhoto || photo) && (
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                setUploadedPhoto(null);
                                                setPhoto(null);
                                                // Don't clear errors - let validation show that photo is required
                                            }}
                                        >
                                            Remove Photo
                                        </Button>
                                    )}
                                </div>
                            </div>
                            <FormMessage className="text-red-500" />
                        </CardContent>
                    </Card>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="document"
                        render={({ field, fieldState }) => (
                            <Card className={`shadow-sm ${fieldState.error ? 'border-red-500 border-2' : ''}`}>
                                <CardHeader>
                                    <CardTitle className="text-lg font-medium">Identity Document</CardTitle>
                                    <CardDescription>
                                        Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <FormItem>
                                        {!uploadedDocument ? (
                                            <FormControl>
                                                <div className="flex items-center justify-center w-full">
                                                    <label className="flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                            <Upload className="w-10 h-10 mb-3 text-black" />
                                                            <p className="mb-2 text-sm text-gray-700"><span className="font-semibold">Click to upload</span> or drag and drop</p>
                                                            <p className="text-xs text-gray-500">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>
                                                        </div>
                                                        <Input
                                                            id="document"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png"
                                                            className="hidden"
                                                            onChange={(e) => {
                                                                const file = e.target.files?.[0];
                                                                if (file) {
                                                                    if (file.size > 5 * 1024 * 1024) {
                                                                        form.setError('document', { message: 'File size exceeds 5MB limit' });
                                                                        return;
                                                                    }
                                                                    form.clearErrors('document');
                                                                    setUploadedDocument(file);
                                                                    field.onChange(file);
                                                                }
                                                            }}
                                                        />
                                                    </label>
                                                </div>
                                            </FormControl>
                                        ) : (
                                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-[#fff8f3] rounded-full">
                                                            <FileText className="h-5 w-5 text-black" />
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium text-gray-700">{uploadedDocument.name}</p>
                                                            <p className="text-xs text-gray-500">
                                                                {uploadedDocument instanceof File
                                                                    ? formatFileSize(uploadedDocument.size)
                                                                    : 'Previously uploaded document'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="flex space-x-2">
                                                        {uploadedDocument && 'url' in uploadedDocument && (
                                                            <Button
                                                                type="button"
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => {
                                                                    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005';
                                                                    const url = uploadedDocument.url.startsWith('http')
                                                                        ? uploadedDocument.url
                                                                        : `${baseUrl}${uploadedDocument.url.startsWith('/') ? '' : '/'}${uploadedDocument.url}`;
                                                                    window.open(url, '_blank');
                                                                }}
                                                                className="h-8 px-3 border-gray-200"
                                                            >
                                                                View
                                                            </Button>
                                                        )}

                                                        {/* Change document button */}
                                                        <div>
                                                            <Input
                                                                type="file"
                                                                accept=".pdf,.jpg,.jpeg,.png"
                                                                onChange={(e) => {
                                                                    const file = e.target.files?.[0];
                                                                    if (file) {
                                                                        if (file.size > 5 * 1024 * 1024) {
                                                                            form.setError('document', { message: 'File size exceeds 5MB limit' });
                                                                            return;
                                                                        }
                                                                        form.clearErrors('document');
                                                                        setUploadedDocument(file);
                                                                        field.onChange(file);
                                                                    }
                                                                }}
                                                                className="hidden"
                                                                id="document-change"
                                                            />
                                                            <label
                                                                htmlFor="document-change"
                                                                className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium border border-gray-200 bg-white hover:bg-gray-50 h-8 px-3"
                                                            >
                                                                Change
                                                            </label>
                                                        </div>

                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setUploadedDocument(null);
                                                                // Don't clear errors - let validation show that document is required
                                                            }}
                                                            className="h-8 w-8 p-0 border-gray-200"
                                                        >
                                                            <X className="h-4 w-4 text-gray-500" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        <FormDescription className="text-xs text-gray-500 mt-2">
                                            This document will serve to verify your identity and date of birth, with your face clearly visible in the document.                          </FormDescription>
                                        <FormMessage className="text-red-500" />
                                    </FormItem>
                                </CardContent>
                            </Card>
                        )}
                    />

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.back()}
                            disabled={isSaving}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={isSaving}>
                            {isSaving ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Updating...
                                </>
                            ) : (
                                'Update Student'
                            )}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default StudentEditPage;