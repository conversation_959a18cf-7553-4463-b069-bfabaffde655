import React from 'react';
import { FileText, Upload, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { FormControl, FormDescription, FormItem, FormMessage } from '@/components/ui/form';

type DocumentType = File | { name: string; size: number; url: string; type: string } | null;

interface DocumentUploadProps {
  document: DocumentType;
  onDocumentUpload: (file: File) => void;
  onDocumentRemove: () => void;
  onDocumentPreview: (document: DocumentType) => void;
  formatFileSize: (bytes: number) => string;
  error?: string;
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  document,
  onDocumentUpload,
  onDocumentRemove,
  onDocumentPreview,
  formatFileSize,
  error
}) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onDocumentUpload(file);
    }
  };

  return (
    <Card className={`shadow-lg w-full max-w-full overflow-hidden ${error ? 'border-red-500 border-2' : 'border-0'}`}>
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
        <CardTitle className="text-base sm:text-lg font-medium text-gray-800">Identity Document *</CardTitle>
        <CardDescription className="text-gray-600 text-sm">
          Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <FormItem>
          {!document ? (
            <FormControl>
              <div className="flex items-center justify-center w-full">
                <label className="flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-10 h-10 mb-3 text-black" />
                    <p className="mb-2 text-sm text-gray-700">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-gray-500">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>
                  </div>
                  <Input
                    id="document"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </label>
              </div>
            </FormControl>
          ) : (
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-[#fff8f3] rounded-full">
                    <FileText className="h-5 w-5 text-black" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">{document.name}</p>
                    <p className="text-xs text-gray-500">
                      {document instanceof File
                        ? formatFileSize(document.size)
                        : 'Previously uploaded document'}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onDocumentPreview(document)}
                    className="h-8 px-3 border-gray-200 hover:bg-gray-50"
                  >
                    View
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={onDocumentRemove}
                    className="h-8 w-8 p-0 border-gray-200 hover:bg-gray-50"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
          <FormDescription className="text-xs text-gray-500 mt-2">
            This document will serve to verify your identity and date of birth.
          </FormDescription>
          <FormMessage className="text-red-500" />
        </FormItem>
      </CardContent>
    </Card>
  );
};
